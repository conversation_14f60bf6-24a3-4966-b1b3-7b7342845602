export interface CourseFile {
  type: 'file';
  name: string;
  path: string;
  extension: string;
  fileType: 'video' | 'document' | 'image' | 'other';
  size: number;
  isVideo: boolean;
  isDocument: boolean;
  isImage: boolean;
}

export interface CoursePart {
  type: 'module' | 'folder';
  name: string;
  path: string;
  parts: (CourseFile | CoursePart)[];
}

export interface CourseModule {
  type: 'module';
  name: string;
  path: string;
  parts: (CourseFile | CoursePart)[];
}

export interface CourseStructure {
  title: string;
  modules: CourseModule[];
  totalModules: number;
  totalFiles: number;
  totalVideos: number;
  totalDocuments: number;
  estimatedDuration: number;
}

export interface VideoAnalysis {
  overview: string;
  quick_note: string;
  transcript: string;
  glossary: Array<{
    term: string;
    definition: string;
  }>;
  concept_map: {
    nodes: string[];
    edges: Array<{
      from: string;
      to: string;
      relation: string;
    }>;
  };
  slides: Array<{
    title: string;
    content: string;
  }>;
  cheat_sheet?: string[];
}

export interface UserProgress {
  username: string;
  lastAccessed: string;
  currentModule?: string;
  currentPart?: string;
  watchedVideos: string[];
  viewedDocuments?: string[];
  completedModules: string[];
  notes: Array<{
    id: string;
    content: string;
    timestamp: string;
    videoPath?: string;
    videoTime?: number;
  }>;
  totalWatchTime: number;
  moduleProgress?: Record<string, {
    watchTime: number;
    completion: number;
  }>;
  analytics: {
    sessionsCount: number;
    averageSessionTime: number;
    focusAreas: string[];
    learningStreak: number;
    dailyProgress?: Record<string, {
      watchTime: number;
      videosWatched: number;
    }>;
  };
}

export interface SharedProgress {
  users: Record<string, UserProgress>;
  lastUpdated: string;
}