import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream';
import { promisify } from 'util';

const pipelineAsync = promisify(pipeline);

export async function GET(request: NextRequest) {
  console.log('🎬 ===== VIDEO SERVE REQUEST START =====');
  console.log('📥 Request URL:', request.url);
  console.log('🕐 Timestamp:', new Date().toISOString());
  
  try {
    const { searchParams } = new URL(request.url);
    let filePath = searchParams.get('path');
    
    console.log('📋 Original filePath from URL:', JSON.stringify(filePath));
    
    if (!filePath) {
      console.log('❌ No file path provided');
      return NextResponse.json({ error: 'File path is required' }, { status: 400 });
    }
    
    console.log('🔍 Before normalization:', JSON.stringify(filePath));
    // Normalize path separators - convert backslashes to forward slashes
    filePath = filePath.replace(/\\/g, '/');
    console.log('🔧 After normalization:', JSON.stringify(filePath));

    // Construct the full path to the file
    let fullPath: string;
    
    console.log('📁 Working directory:', process.cwd());
    console.log('🔍 Is absolute path:', path.isAbsolute(filePath));
    
    if (path.isAbsolute(filePath)) {
      fullPath = filePath;
      console.log('✅ Using absolute path:', fullPath);
    } else {
      // Try different path constructions for video files
      // Convert forward slashes to platform-specific path separators for file system access
      const normalizedPath = filePath.replace(/\//g, path.sep);
      console.log('🔧 Normalized for filesystem:', JSON.stringify(normalizedPath));
      
      const possiblePaths = [
        path.join(process.cwd(), '..', normalizedPath),
        path.join(process.cwd(), '..', 'Photon Trading Zero to Funded 4.0 (2025) @Trading_Tuts', normalizedPath),
        path.join(process.cwd(), normalizedPath),
        normalizedPath
      ];
      
      console.log('🔍 Trying possible paths:');
      possiblePaths.forEach((p, i) => {
        try {
          const exists = fs.existsSync(p);
          console.log(`  ${i}: ${exists ? '✅' : '❌'} ${p}`);
        } catch (error) {
          console.log(`  ${i}: 🚫 ERROR checking ${p}:`, error);
        }
      });
      
      fullPath = possiblePaths.find(p => {
        try {
          return fs.existsSync(p);
        } catch {
          return false;
        }
      }) || possiblePaths[0];
      
      console.log('🎯 Selected fullPath:', fullPath);
    }
    
    // Security check: ensure the path is within allowed directories
    const courseDir = path.join(process.cwd(), '..');
    const publicDir = path.join(process.cwd(), 'public');
    const normalizedPath = path.normalize(fullPath);
    const normalizedCourseDir = path.normalize(courseDir);
    const normalizedPublicDir = path.normalize(publicDir);
    
    const isInCourseDir = normalizedPath.startsWith(normalizedCourseDir);
    const isInPublicDir = normalizedPath.startsWith(normalizedPublicDir);
    
    if (!isInCourseDir && !isInPublicDir) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if file exists
    console.log('📂 Final file existence check for:', fullPath);
    if (!fs.existsSync(fullPath)) {
      console.log('❌ FILE NOT FOUND!');
      console.log('📋 Original path:', filePath);
      console.log('📁 Working directory:', process.cwd());
      console.log('📁 Full path attempted:', fullPath);
      
      try {
        const parentDir = path.join(process.cwd(), '..');
        console.log('📁 Parent directory:', parentDir);
        console.log('📁 Parent directory contents:', fs.readdirSync(parentDir).slice(0, 10));
        
        // Check if "1. Start Here" folder exists
        const startHereDir = path.join(parentDir, '1. Start Here');
        if (fs.existsSync(startHereDir)) {
          console.log('✅ "1. Start Here" directory exists');
          console.log('📁 Contents:', fs.readdirSync(startHereDir).slice(0, 5));
        } else {
          console.log('❌ "1. Start Here" directory NOT found');
        }
      } catch (error) {
        console.log('🚫 Error checking directories:', error);
      }
      
      return NextResponse.json({ error: `File not found: ${path.basename(filePath)}` }, { status: 404 });
    }
    
    console.log('✅ File exists! Proceeding with serving...');

    const stats = fs.statSync(fullPath);
    const ext = path.extname(filePath).toLowerCase();
    
    // Determine content type
    const contentTypeMap: Record<string, string> = {
      '.mp4': 'video/mp4',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.wmv': 'video/x-ms-wmv',
      '.flv': 'video/x-flv',
      '.mkv': 'video/x-matroska',
      '.m4v': 'video/x-m4v',
      '.pdf': 'application/pdf',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.txt': 'text/plain',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif'
    };

    const contentType = contentTypeMap[ext] || 'application/octet-stream';
    
    // Handle range requests for video streaming
    const range = request.headers.get('range');
    
    if (range && contentType.startsWith('video/')) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : stats.size - 1;
      const chunksize = (end - start) + 1;
      
      const fileStream = fs.createReadStream(fullPath, { start, end });
      
      return new NextResponse(fileStream as any, {
        status: 206,
        headers: {
          'Content-Range': `bytes ${start}-${end}/${stats.size}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunksize.toString(),
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=31536000',
        },
      });
    }

    // For non-range requests or non-video files
    const fileStream = fs.createReadStream(fullPath);
    
    return new NextResponse(fileStream as any, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': stats.size.toString(),
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=31536000',
        'Content-Disposition': `inline; filename="${path.basename(filePath)}"`,
      },
    });

  } catch (error) {
    console.error('Error serving file:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}