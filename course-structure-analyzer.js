const fs = require('fs');
const path = require('path');

const courseBasePath = '.';

function analyzeDirectoryStructure(dirPath, isRoot = false) {
  const items = fs.readdirSync(dirPath, { withFileTypes: true });
  const structure = [];

  items.forEach(item => {
    const itemPath = path.join(dirPath, item.name);
    
    if (item.isDirectory()) {
      const subStructure = analyzeDirectoryStructure(itemPath);
      structure.push({
        type: 'module',
        name: item.name,
        path: itemPath,
        parts: subStructure
      });
    } else {
      const ext = path.extname(item.name).toLowerCase();
      const fileType = getFileType(ext);
      const fileSize = fs.statSync(itemPath).size;
      
      structure.push({
        type: 'file',
        name: item.name,
        path: itemPath,
        extension: ext,
        fileType: fileType,
        size: fileSize,
        isVideo: ext === '.mp4',
        isDocument: ['.pdf', '.docx', '.txt'].includes(ext),
        isImage: ['.png', '.jpg', '.jpeg', '.gif'].includes(ext)
      });
    }
  });

  return structure;
}

function getFileType(extension) {
  const types = {
    '.mp4': 'video',
    '.pdf': 'document',
    '.docx': 'document',
    '.txt': 'document',
    '.ts': 'document',
    '.png': 'image',
    '.jpg': 'image',
    '.jpeg': 'image',
    '.gif': 'image',
    '.url': 'link'
  };
  return types[extension] || 'other';
}

function generateCourseStructure() {
  const structure = analyzeDirectoryStructure(courseBasePath, true);
  
  const courseData = {
    title: "Photon Trading Zero to Funded 4.0 (2025)",
    modules: structure.filter(item => item.type === 'module'),
    totalModules: structure.filter(item => item.type === 'module').length,
    totalFiles: 0,
    totalVideos: 0,
    totalDocuments: 0,
    estimatedDuration: 0
  };

  function countFiles(items) {
    items.forEach(item => {
      if (item.type === 'file') {
        courseData.totalFiles++;
        if (item.isVideo) courseData.totalVideos++;
        if (item.isDocument) courseData.totalDocuments++;
      } else if (item.type === 'module') {
        countFiles(item.parts);
      }
    });
  }

  countFiles(structure);

  return courseData;
}

const courseStructure = generateCourseStructure();
console.log(JSON.stringify(courseStructure, null, 2));

fs.writeFileSync('./course-structure.json', JSON.stringify(courseStructure, null, 2));
console.log('\nCourse structure saved to course-structure.json');