Extracted from: 2. What Is An Edge.ONEDDL.docx
Original file: 5.  Trading An Edge/2. What Is An Edge.ONEDDL.docx
Extraction date: 2025-07-06T09:52:17.199Z
File type: DOCX
================================================================================

What Is An Edge?

5. 🎲 TRADING AN EDGE

A massive part of successful trading is dependant on your mindset and psychology. 

There are many esoteric and deep concepts that we’ll get into much later in the program regarding trading psychology and practical tasks that we can undertake to try to resolve any blockages that you may be struggling with. 

But it is my firm belief that if you can really get a great understanding of what trading an edge is, and really developing that probability-based mindset, then this will likely solve the majority of psychological issues you may run into. 

 

Probability -> is the likelihood of a random event occurring. 

Probability of an event = no. of ways that event can occur / total no. of possible outcomes 

When the random event becomes more complex like in trading, we need a formula to help us calculate our "profit expectancy”.

Profit expectation = (Profit x Probability of Winning) + (Loss x Probability of Losing)

Having a proven mathematical edge with a positive profit expectancy does not guarantee you will make money. 

 

Law of large numbers -> the average of the results obtained from a large number of trials should be close to the expected value and will tend to become closer as more trials are performed.

If you have a proven mathematical edge and profit expectancy, it is in your best interest to play it repeatedly as many times as you possibly can.

The only way you can ensure your edge plays out over time is to keep applying that edge over and over again.

 

I encourage you to conduct your own experiment to prove the law of large numbers theorem for yourself.

👉Download your copy of the coin toss flip experiment spreadsheet here! 👈

Click 'File' -> 'Make a copy'  -> save to your own drive 

I guarantee it will benefit your psychology hugely to see how the random outcomes form a smooth expected value after a large number of flips.