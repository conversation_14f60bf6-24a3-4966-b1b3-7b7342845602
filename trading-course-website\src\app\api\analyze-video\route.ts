import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenAI, Type } from '@google/genai';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { videoPath, checkOnly } = await request.json();
    
    if (!videoPath) {
      return NextResponse.json({ error: 'Video path is required' }, { status: 400 });
    }

    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ error: 'Gemini API key not configured' }, { status: 500 });
    }

    // Create unique analysis path based on video path
    const sanitizedPath = videoPath.replace(/[^a-zA-Z0-9]/g, '_');
    const analysisPath = path.join(process.cwd(), 'public', 'analyses', `${sanitizedPath}.json`);
    
    // Check if analysis already exists
    if (fs.existsSync(analysisPath)) {
      const existingAnalysis = JSON.parse(fs.readFileSync(analysisPath, 'utf8'));
      return NextResponse.json(existingAnalysis);
    }
    
    // If this is just a check, return 404 if no analysis exists
    if (checkOnly) {
      return NextResponse.json({ error: 'No analysis found' }, { status: 404 });
    }

    // Check if video file exists
    const fullVideoPath = path.join(process.cwd(), '..', videoPath);
    if (!fs.existsSync(fullVideoPath)) {
      return NextResponse.json({ error: 'Video file not found' }, { status: 404 });
    }

    const ai = new GoogleGenAI({
      apiKey: apiKey,
    });

    const config = {
      thinkingConfig: {
        thinkingBudget: -1,
      },
      responseMimeType: 'application/json',
      responseSchema: {
        type: Type.OBJECT,
        required: ["overview", "quick_note", "transcript", "glossary", "concept_map", "slides"],
        properties: {
          overview: {
            type: Type.STRING,
            description: "High-level overview of the video's main objectives and takeaways.",
          },
          quick_note: {
            type: Type.STRING,
            description: "Concise summary covering the entire content in brief.",
          },
          transcript: {
            type: Type.STRING,
            description: "Full verbatim transcript with speaker labels.",
          },
          glossary: {
            type: Type.ARRAY,
            description: "List of technical terms and their definitions.",
            items: {
              type: Type.OBJECT,
              required: ["term", "definition"],
              properties: {
                term: {
                  type: Type.STRING,
                },
                definition: {
                  type: Type.STRING,
                },
              },
            },
          },
          concept_map: {
            type: Type.OBJECT,
            description: "Structured concept map of how topics connect.",
            required: ["nodes", "edges"],
            properties: {
              nodes: {
                type: Type.ARRAY,
                items: {
                  type: Type.STRING,
                },
              },
              edges: {
                type: Type.ARRAY,
                items: {
                  type: Type.OBJECT,
                  required: ["from", "to", "relation"],
                  properties: {
                    from: {
                      type: Type.STRING,
                    },
                    to: {
                      type: Type.STRING,
                    },
                    relation: {
                      type: Type.STRING,
                    },
                  },
                },
              },
            },
          },
          slides: {
            type: Type.ARRAY,
            description: "Slide-deck summary: each slide has a title and content.",
            items: {
              type: Type.OBJECT,
              required: ["title", "content"],
              properties: {
                title: {
                  type: Type.STRING,
                },
                content: {
                  type: Type.STRING,
                },
              },
            },
          },
          cheat_sheet: {
            type: Type.ARRAY,
            description: "Optional list of key formulas, rules, or tips.",
            items: {
              type: Type.STRING,
            },
          },
        },
      },
      systemInstruction: [
        {
          text: `You are a professional, forward-thinking AI coach for trading education. For each provided course video, you will:
1. Generate a full transcript with speaker labels.
2. Compile a glossary of all technical terms and trading indicators.
3. Construct a concept map showing how the ideas interrelate (nodes & edges).
4. Create a slide deck summarizing each module (title + content).
5. Produce a concise cheat sheet of key formulas or rules, if applicable.
6. Draft a quick note: a brief overall summary of the entire video.
7. Deliver an overview highlighting main objectives and takeaways.

Output must be valid JSON that strictly conforms to the accompanying schema.`,
        }
      ],
    };

    const model = 'gemini-2.5-pro';

    // Read video file
    const videoBuffer = fs.readFileSync(fullVideoPath);
    const videoBase64 = videoBuffer.toString('base64');

    // Get video info for duration check
    const stats = fs.statSync(fullVideoPath);
    const fileSizeInMB = stats.size / (1024 * 1024);
    
    // For demo purposes, estimate duration (in a real app, you'd use ffprobe)
    const estimatedDurationMinutes = Math.floor(fileSizeInMB / 5); // Very rough estimate
    
    let processedVideoBase64 = videoBase64;
    let actualSpeed = 1.0;
    
    // If video is longer than 60 minutes, we need to speed it up
    if (estimatedDurationMinutes > 60) {
      const requiredSpeed = Math.min(1.5, estimatedDurationMinutes / 60);
      actualSpeed = requiredSpeed;
      // Note: In a real implementation, you would use ffmpeg to speed up the video
      // For now, we'll just pass the original video and note the intended speed
    }

    const contents = [
      {
        role: 'user',
        parts: [
          {
            inlineData: {
              data: processedVideoBase64,
              mimeType: 'video/mp4',
            },
          },
        ],
      },
      {
        role: 'user',
        parts: [
          {
            text: `Please analyze this trading course video and generate the following deliverables as JSON according to the provided schema:

• overview  
• quick_note  
• transcript (with speaker labels)  
• glossary (term + definition)  
• concept_map (nodes & edges)  
• slides (slide deck)  
• cheat_sheet (if applicable)

Video File: ${path.basename(videoPath)}
Video Path: ${videoPath}
${actualSpeed !== 1.0 ? `Note: This video was processed at ${actualSpeed}x speed to fit within analysis limits.` : ''}

Please provide unique, detailed analysis specific to THIS video's content. Do not use generic trading concepts.`,
          },
        ],
      },
    ];

    const response = await ai.models.generateContent({
      model,
      config,
      contents,
    });

    const analysis = JSON.parse(response.text || '{}');
    
    // Save analysis for future use
    const analysisDir = path.join(process.cwd(), 'public', 'analyses');
    if (!fs.existsSync(analysisDir)) {
      fs.mkdirSync(analysisDir, { recursive: true });
    }
    
    fs.writeFileSync(analysisPath, JSON.stringify(analysis, null, 2));

    return NextResponse.json(analysis);
  } catch (error) {
    console.error('Error analyzing video:', error);
    return NextResponse.json(
      { error: 'Failed to analyze video' },
      { status: 500 }
    );
  }
}