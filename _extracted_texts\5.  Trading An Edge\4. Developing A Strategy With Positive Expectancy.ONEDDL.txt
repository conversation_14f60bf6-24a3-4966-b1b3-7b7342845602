Extracted from: 4. Developing A Strategy With Positive Expectancy.ONEDDL.docx
Original file: 5.  Trading An Edge/4. Developing A Strategy With Positive Expectancy.ONEDDL.docx
Extraction date: 2025-07-06T09:52:17.231Z
File type: DOCX
================================================================================

Developing A Strategy With Positive Expectancy

5. 🎲 TRADING AN EDGE

Edge in the market -> is simply having a positive profit expectancy

There are 4 core methods we can use as traders to increase our edge:

Increase our win rate

Decrease our loss rate

Increase profit on our winning trades

Decrease losses on our losing trades

Calculating the probability of a coin toss or a roulette game is simple as the number of ways your desired outcome can occur is fixed. (There is only one head on a coin and only 18 black numbers on the wheel).

In trading, however, the number of different ways you can make a profit is pretty much infinite. 

So you can never calculate the exact profit expectancy in trading. You can only look at:

Previous live results

Backtested results 

 

Profit expectancy -> how much we stand to gain or lose as a trader for every £ risked

Profit expectancy = (Profit x Probability of Winning) + (Loss x Probability of Losing)

But in trading, the amount you can lose or win isn't always fixed like a coin toss or on a roulette table, so we have to adjust the formula:

Profit expectancy = (Average Profit x Probability of Winning) + (Average Loss x Probability of Losing)

Profit expectancy is based on a combination of your Strike Rate and your Average P&L. 

 

It’s impossible to always be right when trading forex. However, calculating your expectancy can help you to shift your focus away from trying to be ‘right’ on each individual trade, to instead how right you are overall.

 

High Strike Rate -> strategies that win more times than they lose.

High Reward Risk -> strategies that win big, but not necessarily often. 

The higher your average R:R, the less often you need to win. 

 

Required strike rate to breakeven = 1 / 1*(1 + R-Multiple)

With a 3:1 R:R, you can be wrong up to 75% of the time and still break even! 

 

Trading is all about how much money you make when you are right, and how much you lose when you are wrong.