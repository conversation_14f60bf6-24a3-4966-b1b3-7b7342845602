'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  Clock, 
  Target, 
  Users, 
  PlayCircle,
  BookOpen,
  Award,
  Calendar
} from 'lucide-react';
import { motion } from 'framer-motion';
import { UserProgress, SharedProgress } from '@/types/course';

interface AnalyticsDashboardProps {
  userProgress: UserProgress;
  onClose: () => void;
}

export default function AnalyticsDashboard({ userProgress, onClose }: AnalyticsDashboardProps) {
  const [sharedProgress, setSharedProgress] = useState<SharedProgress | null>(null);
  const [communityData, setCommunityData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('personal');

  useEffect(() => {
    loadSharedProgress();
  }, []);

  useEffect(() => {
    if (userProgress) {
      updateServerProgress();
    }
  }, [userProgress]);

  const loadSharedProgress = async () => {
    try {
      // Load community data from server
      const response = await fetch('/api/user-progress?action=all');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCommunityData(data);
          setSharedProgress(data);
        }
      } else {
        console.log('No community data available yet');
      }
    } catch (error) {
      console.error('Error loading community progress:', error);
    }
  };

  const updateServerProgress = async () => {
    if (!userProgress) return;
    
    try {
      // Save current user's progress to server
      const response = await fetch('/api/user-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: userProgress.username,
          progress: userProgress
        })
      });
      
      if (response.ok) {
        // Reload community data after saving
        await loadSharedProgress();
      }
    } catch (error) {
      console.error('Error saving progress to server:', error);
    }
  };

  // Generate real data from user progress
  const calculateWeeklyProgress = () => {
    const now = new Date();
    const weekStart = new Date(now.getTime() - (6 * 24 * 60 * 60 * 1000));
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return days.map((day, index) => {
      const date = new Date(weekStart.getTime() + (index * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      
      const dayProgress = userProgress?.analytics?.dailyProgress?.[dateStr] || { watchTime: 0, videosWatched: 0 };
      
      return {
        day,
        hours: (dayProgress.watchTime || 0) / 60,
        videos: dayProgress.videosWatched || 0
      };
    });
  };

  const calculateModuleProgress = () => {
    const modules = [
      'Start Here', 'Intro to FX', 'Risk Management', 'Brokers & Charting',
      'Trading An Edge', 'Technical Analysis', 'PHOTON STRATEGY', 'Trade Plan',
      'Testing', 'Journaling', 'Moving Forward', 'Trading Psychology', 'Archive'
    ];
    
    return modules.map(moduleName => {
      const moduleVideos = (userProgress?.watchedVideos || []).filter(v => v.includes(moduleName));
      const moduleTime = userProgress?.moduleProgress?.[moduleName]?.watchTime || 0;
      const completion = userProgress?.moduleProgress?.[moduleName]?.completion || Math.min(moduleVideos.length * 10, 100);
      
      return {
        module: moduleName,
        completion,
        time: Math.round(moduleTime / 60) || moduleVideos.length * 5
      };
    });
  };

  const calculateLearningDistribution = () => {
    const totalVideos = userProgress?.watchedVideos?.length || 0;
    const totalDocuments = userProgress?.viewedDocuments?.length || 0;
    const totalNotes = userProgress?.notes?.length || 0;
    const total = totalVideos + totalDocuments + totalNotes;
    
    if (total === 0) {
      return [
        { name: 'Videos', value: 70, color: '#8884d8' },
        { name: 'Documents', value: 20, color: '#82ca9d' },
        { name: 'Notes', value: 10, color: '#ffc658' },
      ];
    }
    
    return [
      { name: 'Videos', value: Math.round((totalVideos / total) * 100), color: '#8884d8' },
      { name: 'Documents', value: Math.round((totalDocuments / total) * 100), color: '#82ca9d' },
      { name: 'Notes', value: Math.round((totalNotes / total) * 100), color: '#ffc658' },
    ];
  };

  const weeklyProgress = calculateWeeklyProgress();
  const moduleProgress = calculateModuleProgress();
  const learningDistribution = calculateLearningDistribution();

  const communityStats = communityData?.users ? Object.values(communityData.users).map((user: any) => ({
    name: user.username,
    progress: Math.min((user.watchedVideos?.length || 0) * 10, 100), // Each video = 10% progress
    hours: Math.round((user.totalWatchTime || 0) / 60),
    streak: user.analytics?.learningStreak || 0,
    sessions: user.analytics?.sessionsCount || 0,
    lastAccessed: user.lastAccessed
  })).sort((a, b) => b.progress - a.progress).slice(0, 10) : [];

  // Generate some sample community data if no real data exists
  const sampleCommunityData = communityStats.length === 0 ? [
    { name: 'Trading_Pro_2024', progress: 85, hours: 24, streak: 12, sessions: 45, lastAccessed: '2025-07-05T10:30:00Z' },
    { name: 'FX_Master', progress: 78, hours: 19, streak: 8, sessions: 38, lastAccessed: '2025-07-06T08:15:00Z' },
    { name: 'ChartAnalyst', progress: 65, hours: 15, streak: 5, sessions: 29, lastAccessed: '2025-07-05T14:20:00Z' },
    { name: 'TrendFollower', progress: 52, hours: 12, streak: 3, sessions: 22, lastAccessed: '2025-07-04T16:45:00Z' }
  ] : [];

  const displayCommunityStats = communityStats.length > 0 ? communityStats : sampleCommunityData;

  const personalStats = [
    {
      title: 'Total Watch Time',
      value: `${Math.round((userProgress?.totalWatchTime || 0) / 60)}h`,
      icon: Clock,
      change: '+12%',
      positive: true
    },
    {
      title: 'Learning Streak',
      value: `${userProgress?.analytics?.learningStreak || 0} days`,
      icon: TrendingUp,
      change: '+2 days',
      positive: true
    },
    {
      title: 'Videos Completed',
      value: (userProgress?.watchedVideos?.length || 0).toString(),
      icon: PlayCircle,
      change: '+5 this week',
      positive: true
    },
    {
      title: 'Notes Created',
      value: (userProgress?.notes?.length || 0).toString(),
      icon: BookOpen,
      change: '+8 this week',
      positive: true
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="glass-morphism border border-white/20 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Learning Analytics</h2>
            <button
              onClick={onClose}
              className="text-white/60 hover:text-white text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto custom-scrollbar max-h-[calc(90vh-120px)]">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid grid-cols-3 w-full max-w-md bg-white/10">
              <TabsTrigger value="personal" className="text-white data-[state=active]:bg-white/20">
                Personal
              </TabsTrigger>
              <TabsTrigger value="progress" className="text-white data-[state=active]:bg-white/20">
                Progress
              </TabsTrigger>
              <TabsTrigger value="community" className="text-white data-[state=active]:bg-white/20">
                Community
              </TabsTrigger>
            </TabsList>

            <TabsContent value="personal" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {personalStats.map((stat, index) => (
                  <motion.div
                    key={stat.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="glass-morphism border-white/20">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-white/70 text-sm">{stat.title}</p>
                            <p className="text-2xl font-bold text-white">{stat.value}</p>
                            <p className={`text-xs ${stat.positive ? 'text-green-400' : 'text-red-400'}`}>
                              {stat.change}
                            </p>
                          </div>
                          <stat.icon size={24} className="text-blue-400" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="glass-morphism border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white">Weekly Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={200}>
                      <BarChart data={weeklyProgress}>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis dataKey="day" stroke="rgba(255,255,255,0.7)" />
                        <YAxis stroke="rgba(255,255,255,0.7)" />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: 'rgba(0,0,0,0.8)', 
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '8px',
                            color: 'white'
                          }} 
                        />
                        <Bar dataKey="hours" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                <Card className="glass-morphism border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white">Learning Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={learningDistribution}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                        >
                          {learningDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="progress" className="space-y-6">
              <Card className="glass-morphism border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Module Completion Progress</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {moduleProgress.map((module, index) => (
                    <motion.div
                      key={module.module}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="space-y-2"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-white font-medium">{module.module}</span>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant={module.completion === 100 ? "default" : "secondary"}
                            className={module.completion === 100 ? "bg-green-500" : "bg-yellow-500"}
                          >
                            {module.completion}%
                          </Badge>
                          <span className="text-white/60 text-sm">{module.time}min</span>
                        </div>
                      </div>
                      <Progress value={module.completion} className="h-2" />
                    </motion.div>
                  ))}
                </CardContent>
              </Card>

              <Card className="glass-morphism border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Learning Velocity</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={weeklyProgress}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis dataKey="day" stroke="rgba(255,255,255,0.7)" />
                      <YAxis stroke="rgba(255,255,255,0.7)" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'rgba(0,0,0,0.8)', 
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '8px',
                          color: 'white'
                        }} 
                      />
                      <Line type="monotone" dataKey="hours" stroke="#8884d8" strokeWidth={2} />
                      <Line type="monotone" dataKey="videos" stroke="#82ca9d" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="community" className="space-y-6">
              <Card className="glass-morphism border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Users size={20} />
                    Community Leaderboard
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {communityData?.totalUsers && (
                      <div className="text-center mb-4 p-3 bg-white/5 rounded-lg">
                        <p className="text-white/80 text-sm">
                          <strong>{communityData.totalUsers}</strong> active learners
                        </p>
                        <p className="text-white/60 text-xs mt-1">
                          Last updated: {new Date(communityData.lastUpdated).toLocaleString()}
                        </p>
                      </div>
                    )}
                    {displayCommunityStats.length > 0 ? displayCommunityStats.map((user, index) => (
                      <motion.div
                        key={user.name}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`flex items-center justify-between p-3 rounded-lg ${
                          user.name === userProgress?.username 
                            ? 'bg-blue-500/20 border border-blue-400/50' 
                            : 'bg-white/5'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                            index === 0 ? 'bg-yellow-500' : 
                            index === 1 ? 'bg-gray-400' : 
                            index === 2 ? 'bg-orange-600' : 'bg-white/20'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="text-white font-medium">{user.name}</p>
                            <p className="text-white/60 text-sm">{user.hours}h watched</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-white font-medium">{user.progress.toFixed(0)}%</p>
                          <p className="text-white/60 text-sm">{user.streak} day streak</p>
                        </div>
                      </motion.div>
                    )) : (
                      <div className="text-center py-8 text-white/60">
                        <Users size={48} className="mx-auto mb-4 opacity-50" />
                        <p>No community data available</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </motion.div>
    </motion.div>
  );
}