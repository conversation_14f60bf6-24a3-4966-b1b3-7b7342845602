Extracted from: 3. Backtesting.ONEDDL.docx
Original file: 9.  Testing/3. Backtesting.ONEDDL.docx
Extraction date: 2025-07-06T09:52:18.200Z
File type: DOCX
================================================================================

Backtesting

8. 🧪 TESTING

BACKTESTING -> practice skillset of trade plan execution on historical price action in a simulated market environment.  

 

Perform this process only once you have developed a solid understanding of your strategy through building out your case studies (>20) and performed at least one month of markups. 

 

Pick a random date to replay price from on the HTFs 

Perform your mapping process 

Fast forward to the start of the session(s) you trade 

Slowly play price forward to look for setups in line with your plan 

Journal trades as if you executed them in a live market (try not to cheat!) 

Once finished, take the time to review your entire backtesting session to look for key lessons and improvements to your plan or personal performance 

 

👉  CLICK HERE to download the notion template IF you don't already have it 👈