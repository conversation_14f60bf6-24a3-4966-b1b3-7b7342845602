import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased radial-gradient(circle at 20% 80%, #7877c64d 0%, #0000 50%), radial-gradient(circle at 80% 20%, #ff77c64d 0%, #0000 50%), radial-gradient(circle at 40% 40%, #78dbe24d 0%, #0000 50%)`}
      >
        {children}
      </body>
    </html>
  );
}
