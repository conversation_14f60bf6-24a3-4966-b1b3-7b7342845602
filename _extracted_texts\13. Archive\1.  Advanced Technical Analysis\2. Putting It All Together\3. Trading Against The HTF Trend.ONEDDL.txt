Extracted from: 3. Trading Against The HTF Trend.ONEDDL.docx
Original file: 13. Archive/1.  Advanced Technical Analysis/2. Putting It All Together/3. Trading Against The HTF Trend.ONEDDL.docx
Extraction date: 2025-07-06T09:52:16.948Z
File type: DOCX
================================================================================

Trading Against The HTF Trend

Putting It All Together

In this lesson, I meticulously build the HTF story with a heavy focus on identifying swing and internal structure.

I also cover POI selection so that you can keep your strike rate as high as possible specifically when trading against the HTF trend.

 

To add to the key points from the previous lesson:

How To Pick High Probability POIs To Look For LTF Trades In and Around [PART 2]

A cumulation of factors help us determine if a POI is of good quality or not (probabilistically), including but not limited to:

What is the overall HTF story? (Pro/counter-trend, how many TFs are aligned?)

How well priced is the M15 POI? (Premium vs discount on M15 & 4H)

What did the M15 POI achieve? (BOS / Flip / both)

Is the M15 POI a sweep zone? (Grabbed liquidity when it formed)

Is there structural inducement in front of the M15 POI? (Otherwise the POI itself may become inducement)

Is it stacked with another zone? (M15 within a 4H)

Is the M15 POI a range-created zone or a pivot-created zone? (The more refined [smaller] you have it, the higher the strike rate of the M1 entry model - range zones can give early traps before the real move - so you can wait for an M15 CHoCH first from the range if you use range zones)

How is price being delivered to the zone? (Impulsive vs corrective)

 

To keep it even more simple, 2 main things you want to look for when price reaches an M15 POI to increase the strike rate of an M1 entry:

Sweep of liquidity -> the first M1 CHoCH in an M15 POI can be a trap, that’s why we wait for a sweep of liquidity

Mitigation of a refined M1 POI within the M15 POI -> increasing the timing/accuracy

 

If you just enter on the first M1 CHoCH the moment any ‘random’ POI is hit you will likely end up with a really low strike rate.

This is why the focus is on selecting high probability POIs based on all of those points above.

And the entry model is: liquidation -> mitigation -> CHoCH -> Flip (sweep) -> enter

Rather than just: mitigation -> CHoCH -> enter

 

So those above are all of the questions I’m essentially asking myself before I even think about an entry.

 

When starting out, you can use simple mechanical rules to follow such as:

4H POI -> Wait for M15 CHoCH

M15 POI -> Wait for M1 CHoCH

M15 within 4H POI -> Wait for M1 CHoCH

You may want to adjust these depending on whether pro/counter-trend specific timeframes etc, but try to keep it simple!!!