'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Lock, User, ArrowRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface AuthFlowProps {
  onAuthenticated: (username: string) => void;
}

const COURSE_PASSWORD = process.env.NEXT_PUBLIC_COURSE_PASSWORD || 'photon2025'; 

export default function AuthFlow({ onAuthenticated }: AuthFlowProps) {
  const [step, setStep] = useState<'password' | 'username'>('password');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    setTimeout(() => {
      if (password === COURSE_PASSWORD) {
        setStep('username');
      } else {
        setError('Invalid password. Please try again.');
      }
      setIsLoading(false);
    }, 1000);
  };

  const handleUsernameSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    setTimeout(() => {
      if (username.trim().length >= 2) {
        localStorage.setItem('courseAuth', JSON.stringify({
          username: username.trim(),
          loginTime: new Date().toISOString()
        }));
        onAuthenticated(username.trim());
      } else {
        setError('Username must be at least 2 characters long.');
      }
      setIsLoading(false);
    }, 1000);
  };

  useEffect(() => {
    const auth = localStorage.getItem('courseAuth');
    if (auth) {
      try {
        const { username } = JSON.parse(auth);
        if (username) {
          onAuthenticated(username);
        }
      } catch (error) {
        localStorage.removeItem('courseAuth');
      }
    }
  }, [onAuthenticated]);

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md"
      >
        <Card className="glass-morphism border-white/20">
          <CardHeader className="text-center pb-2">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              <CardTitle className="text-3xl font-bold text-white mb-2 drop-shadow-lg">
                Photon Trading
              </CardTitle>
              <p className="text-white/90 text-sm">
                Zero to Funded 4.0 (2025)
              </p>
            </motion.div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <AnimatePresence mode="wait">
              {step === 'password' ? (
                <motion.div
                  key="password"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form onSubmit={handlePasswordSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-white/90">
                        <Lock size={16} />
                        <span className="text-sm font-medium">Course Password</span>
                      </div>
                      <Input
                        type="password"
                        placeholder="Enter course password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="bg-white/20 border-white/30 text-white placeholder:text-white/60 focus:border-white/50 backdrop-blur-sm"
                        disabled={isLoading}
                        autoFocus
                      />
                    </div>
                    
                    {error && (
                      <motion.p 
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-red-300 text-sm"
                      >
                        {error}
                      </motion.p>
                    )}
                    
                    <Button
                      type="submit"
                      className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white backdrop-blur-sm"
                      disabled={isLoading || !password}
                    >
                      {isLoading ? (
                        <motion.div 
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
                        />
                      ) : (
                        <>
                          Continue
                          <ArrowRight size={16} className="ml-2" />
                        </>
                      )}
                    </Button>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  key="username"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form onSubmit={handleUsernameSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-white/90">
                        <User size={16} />
                        <span className="text-sm font-medium">Your Name</span>
                      </div>
                      <Input
                        type="text"
                        placeholder="Enter your name"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="bg-white/20 border-white/30 text-white placeholder:text-white/60 focus:border-white/50 backdrop-blur-sm"
                        disabled={isLoading}
                        autoFocus
                      />
                    </div>
                    
                    {error && (
                      <motion.p 
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-red-300 text-sm"
                      >
                        {error}
                      </motion.p>
                    )}
                    
                    <Button
                      type="submit"
                      className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white backdrop-blur-sm"
                      disabled={isLoading || !username.trim()}
                    >
                      {isLoading ? (
                        <motion.div 
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
                        />
                      ) : (
                        <>
                          Enter Course
                          <ArrowRight size={16} className="ml-2" />
                        </>
                      )}
                    </Button>
                  </form>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
        
        <motion.p 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="text-center text-white/80 text-xs mt-6"
        >
          Premium Trading Education Platform
        </motion.p>
      </motion.div>
    </div>
  );
}