{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"glass-morphism-dark text-white border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-300\",\n        destructive:\n          \"glass-morphism-dark text-white border border-red-400/30 bg-red-500/20 hover:bg-red-500/30 hover:border-red-400/50\",\n        outline:\n          \"glass-morphism-dark text-white border border-white/20 hover:bg-white/10 hover:border-white/30\",\n        secondary:\n          \"glass-morphism-dark text-white border border-white/15 bg-white/5 hover:bg-white/10 hover:border-white/25\",\n        ghost:\n          \"text-white hover:bg-white/10 backdrop-blur-sm transition-all duration-300\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/visualization/ConceptMap.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useState, useCallback } from 'react';\r\nimport { VideoAnalysis } from '@/types/course';\r\nimport { Button } from '@/components/ui/button';\r\nimport { ZoomIn, ZoomOut, RotateCcw, Maximize2 } from 'lucide-react';\r\n\r\ninterface ConceptMapProps {\r\n  conceptMap: VideoAnalysis['concept_map'];\r\n  onOpenDialog?: () => void;\r\n}\r\n\r\nexport default function ConceptMap({ conceptMap, onOpenDialog }: ConceptMapProps) {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const [scale, setScale] = useState(1);\r\n  const [panX, setPanX] = useState(0);\r\n  const [panY, setPanY] = useState(0);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });\r\n\r\n  const drawConceptMap = useCallback(() => {\r\n    if (!conceptMap || !canvasRef.current) return;\r\n\r\n    const canvas = canvasRef.current;\r\n    const ctx = canvas.getContext('2d');\r\n    if (!ctx) return;\r\n\r\n    // Set canvas size\r\n    canvas.width = canvas.offsetWidth * window.devicePixelRatio;\r\n    canvas.height = canvas.offsetHeight * window.devicePixelRatio;\r\n    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);\r\n\r\n    const width = canvas.offsetWidth;\r\n    const height = canvas.offsetHeight;\r\n\r\n    // Clear canvas\r\n    ctx.clearRect(0, 0, width, height);\r\n\r\n    // Apply transformations\r\n    ctx.save();\r\n    ctx.translate(panX, panY);\r\n    ctx.scale(scale, scale);\r\n\r\n    // Node positioning\r\n    const nodes = conceptMap.nodes.map((node, index) => ({\r\n      name: node,\r\n      x: (Math.cos((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (width / 2 - 80) + 80,\r\n      y: (Math.sin((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (height / 2 - 40) + 40,\r\n      radius: 30 + node.length * 2\r\n    }));\r\n\r\n    // Draw edges\r\n    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';\r\n    ctx.lineWidth = 2;\r\n    conceptMap.edges.forEach(edge => {\r\n      const fromNode = nodes.find(n => n.name === edge.from);\r\n      const toNode = nodes.find(n => n.name === edge.to);\r\n      \r\n      if (fromNode && toNode) {\r\n        ctx.beginPath();\r\n        ctx.moveTo(fromNode.x, fromNode.y);\r\n        ctx.lineTo(toNode.x, toNode.y);\r\n        ctx.stroke();\r\n\r\n        // Draw arrow\r\n        const angle = Math.atan2(toNode.y - fromNode.y, toNode.x - fromNode.x);\r\n        const arrowLength = 10;\r\n        ctx.beginPath();\r\n        ctx.moveTo(\r\n          toNode.x - toNode.radius * Math.cos(angle),\r\n          toNode.y - toNode.radius * Math.sin(angle)\r\n        );\r\n        ctx.lineTo(\r\n          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle - 0.5),\r\n          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle - 0.5)\r\n        );\r\n        ctx.moveTo(\r\n          toNode.x - toNode.radius * Math.cos(angle),\r\n          toNode.y - toNode.radius * Math.sin(angle)\r\n        );\r\n        ctx.lineTo(\r\n          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle + 0.5),\r\n          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle + 0.5)\r\n        );\r\n        ctx.stroke();\r\n\r\n        // Draw relation label\r\n        const midX = (fromNode.x + toNode.x) / 2;\r\n        const midY = (fromNode.y + toNode.y) / 2;\r\n        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';\r\n        ctx.font = '10px \"Fira Code\"';\r\n        ctx.textAlign = 'center';\r\n        ctx.fillText(edge.relation, midX, midY - 5);\r\n      }\r\n    });\r\n\r\n    // Draw nodes\r\n    nodes.forEach((node, index) => {\r\n      // Node background\r\n      const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, node.radius);\r\n      gradient.addColorStop(0, `hsla(${200 + index * 30}, 70%, 60%, 0.8)`);\r\n      gradient.addColorStop(1, `hsla(${200 + index * 30}, 70%, 40%, 0.6)`);\r\n      \r\n      ctx.fillStyle = gradient;\r\n      ctx.beginPath();\r\n      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);\r\n      ctx.fill();\r\n\r\n      // Node border\r\n      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';\r\n      ctx.lineWidth = 2;\r\n      ctx.stroke();\r\n\r\n      // Node text\r\n      ctx.fillStyle = '#ffffff';\r\n      ctx.font = '12px \"Fira Code\"';\r\n      ctx.textAlign = 'center';\r\n      ctx.textBaseline = 'middle';\r\n      \r\n      // Wrap text for long nodes\r\n      const words = node.name.split(' ');\r\n      if (words.length > 2) {\r\n        const line1 = words.slice(0, Math.ceil(words.length / 2)).join(' ');\r\n        const line2 = words.slice(Math.ceil(words.length / 2)).join(' ');\r\n        ctx.fillText(line1, node.x, node.y - 6);\r\n        ctx.fillText(line2, node.x, node.y + 6);\r\n      } else {\r\n        ctx.fillText(node.name, node.x, node.y);\r\n      }\r\n    });\r\n\r\n    // Restore canvas state\r\n    ctx.restore();\r\n  }, [conceptMap, scale, panX, panY]);\r\n\r\n  useEffect(() => {\r\n    drawConceptMap();\r\n  }, [drawConceptMap]);\r\n\r\n  const handleMouseDown = (e: React.MouseEvent) => {\r\n    setIsDragging(true);\r\n    setLastMousePos({ x: e.clientX, y: e.clientY });\r\n  };\r\n\r\n  const handleMouseMove = (e: React.MouseEvent) => {\r\n    if (!isDragging) return;\r\n\r\n    const deltaX = e.clientX - lastMousePos.x;\r\n    const deltaY = e.clientY - lastMousePos.y;\r\n\r\n    setPanX(prev => prev + deltaX);\r\n    setPanY(prev => prev + deltaY);\r\n\r\n    setLastMousePos({ x: e.clientX, y: e.clientY });\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleWheel = (e: React.WheelEvent) => {\r\n    e.preventDefault();\r\n    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;\r\n    setScale(prev => Math.max(0.1, Math.min(3, prev * zoomFactor)));\r\n  };\r\n\r\n  const resetView = () => {\r\n    setScale(1);\r\n    setPanX(0);\r\n    setPanY(0);\r\n  };\r\n\r\n  const zoomIn = () => {\r\n    setScale(prev => Math.min(3, prev * 1.2));\r\n  };\r\n\r\n  const zoomOut = () => {\r\n    setScale(prev => Math.max(0.1, prev * 0.8));\r\n  };\r\n\r\n  if (!conceptMap || conceptMap.nodes.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-8 text-white/60\">\r\n        <p>No concept map available</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full h-64 bg-gray-900 rounded-lg border border-gray-700 relative\">\r\n      {/* Control buttons */}\r\n      <div className=\"absolute top-2 right-2 z-10 flex gap-1\">\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          onClick={zoomIn}\r\n          className=\"bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700\"\r\n        >\r\n          <ZoomIn size={14} />\r\n        </Button>\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          onClick={zoomOut}\r\n          className=\"bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700\"\r\n        >\r\n          <ZoomOut size={14} />\r\n        </Button>\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          onClick={resetView}\r\n          className=\"bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700\"\r\n        >\r\n          <RotateCcw size={14} />\r\n        </Button>\r\n        {onOpenDialog && (\r\n          <Button\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n            onClick={onOpenDialog}\r\n            className=\"bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700\"\r\n          >\r\n            <Maximize2 size={14} />\r\n          </Button>\r\n        )}\r\n      </div>\r\n\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"w-full h-full rounded-lg\"\r\n        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}\r\n        onMouseDown={handleMouseDown}\r\n        onMouseMove={handleMouseMove}\r\n        onMouseUp={handleMouseUp}\r\n        onMouseLeave={handleMouseUp}\r\n        onWheel={handleWheel}\r\n      />\r\n\r\n      {/* Scale indicator */}\r\n      <div className=\"absolute bottom-2 left-2 bg-gray-800/80 text-white text-xs px-2 py-1 rounded\">\r\n        {Math.round(scale * 100)}%\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AALA;;;;;AAYe,SAAS,WAAW,EAAE,UAAU,EAAE,YAAY,EAAmB;IAC9E,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE9D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,cAAc,CAAC,UAAU,OAAO,EAAE;QAEvC,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,kBAAkB;QAClB,OAAO,KAAK,GAAG,OAAO,WAAW,GAAG,OAAO,gBAAgB;QAC3D,OAAO,MAAM,GAAG,OAAO,YAAY,GAAG,OAAO,gBAAgB;QAC7D,IAAI,KAAK,CAAC,OAAO,gBAAgB,EAAE,OAAO,gBAAgB;QAE1D,MAAM,QAAQ,OAAO,WAAW;QAChC,MAAM,SAAS,OAAO,YAAY;QAElC,eAAe;QACf,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;QAE3B,wBAAwB;QACxB,IAAI,IAAI;QACR,IAAI,SAAS,CAAC,MAAM;QACpB,IAAI,KAAK,CAAC,OAAO;QAEjB,mBAAmB;QACnB,MAAM,QAAQ,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBACnD,MAAM;gBACN,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,IAAI,KAAK,EAAE,GAAI,WAAW,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,IAAI;gBACxF,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,IAAI,KAAK,EAAE,GAAI,WAAW,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI;gBACzF,QAAQ,KAAK,KAAK,MAAM,GAAG;YAC7B,CAAC;QAED,aAAa;QACb,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,WAAW,KAAK,CAAC,OAAO,CAAC,CAAA;YACvB,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI;YACrD,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,EAAE;YAEjD,IAAI,YAAY,QAAQ;gBACtB,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;gBACjC,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;gBAC7B,IAAI,MAAM;gBAEV,aAAa;gBACb,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;gBACrE,MAAM,cAAc;gBACpB,IAAI,SAAS;gBACb,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,QACpC,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC;gBAEtC,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ,MAC5E,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ;gBAE9E,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,QACpC,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC;gBAEtC,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ,MAC5E,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ;gBAE9E,IAAI,MAAM;gBAEV,sBAAsB;gBACtB,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI;gBACvC,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI;gBACvC,IAAI,SAAS,GAAG;gBAChB,IAAI,IAAI,GAAG;gBACX,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM,OAAO;YAC3C;QACF;QAEA,aAAa;QACb,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,kBAAkB;YAClB,MAAM,WAAW,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM;YACxF,SAAS,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC;YACnE,SAAS,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC;YAEnE,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,EAAE,GAAG,IAAI,KAAK,EAAE;YACnD,IAAI,IAAI;YAER,cAAc;YACd,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,MAAM;YAEV,YAAY;YACZ,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAChB,IAAI,YAAY,GAAG;YAEnB,2BAA2B;YAC3B,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC;gBAC/D,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC;gBAC5D,IAAI,QAAQ,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG;gBACrC,IAAI,QAAQ,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG;YACvC,OAAO;gBACL,IAAI,QAAQ,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;YACxC;QACF;QAEA,uBAAuB;QACvB,IAAI,OAAO;IACb,GAAG;QAAC;QAAY;QAAO;QAAM;KAAK;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,kBAAkB,CAAC;QACvB,cAAc;QACd,gBAAgB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;IAC/C;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY;QAEjB,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;QACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;QAEzC,QAAQ,CAAA,OAAQ,OAAO;QACvB,QAAQ,CAAA,OAAQ,OAAO;QAEvB,gBAAgB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;IAC/C;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,MAAM,aAAa,EAAE,MAAM,GAAG,IAAI,MAAM;QACxC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO;IACpD;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,SAAS;QACb,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IACtC;IAEA,MAAM,UAAU;QACd,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,KAAK,OAAO;IACxC;IAEA,IAAI,CAAC,cAAc,WAAW,KAAK,CAAC,MAAM,KAAK,GAAG;QAChD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,MAAM;;;;;;;;;;;kCAEhB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;4BAAC,MAAM;;;;;;;;;;;kCAEjB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;;;;;;oBAElB,8BACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,QAAQ,aAAa,aAAa;gBAAO;gBAClD,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;;oBACZ,KAAK,KAAK,CAAC,QAAQ;oBAAK;;;;;;;;;;;;;AAIjC", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/video/CustomVideoPlayer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useState } from 'react';\r\nimport { VideoAnalysis } from '@/types/course';\r\nimport ConceptMap from '@/components/visualization/ConceptMap';\r\nimport VideoAnalysisDialog from './VideoAnalysisDialog';\r\n\r\ninterface CustomVideoPlayerProps {\r\n  src: string;\r\n  analysis?: VideoAnalysis;\r\n  onTimeUpdate?: (currentTime: number) => void;\r\n  onProgress?: (progress: number) => void;\r\n  initialTime?: number;\r\n}\r\n\r\nexport default function CustomVideoPlayer({\r\n  src,\r\n  analysis,\r\n  onTimeUpdate,\r\n  onProgress,\r\n  initialTime = 0\r\n}: CustomVideoPlayerProps) {\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [dialogTab, setDialogTab] = useState<'overview' | 'concept_map' | 'slides' | 'transcript' | 'glossary' | 'cheat_sheet'>('overview');\r\n\r\n  useEffect(() => {\r\n    const video = videoRef.current;\r\n    if (!video) {\r\n      return;\r\n    }\r\n\r\n    const handleTimeUpdate = () => {\r\n      onTimeUpdate?.(video.currentTime);\r\n    };\r\n\r\n    const handleProgress = () => {\r\n      if (video.duration > 0) {\r\n        const progress = (video.currentTime / video.duration) * 100;\r\n        onProgress?.(progress);\r\n      }\r\n    };\r\n\r\n    const handleLoadedMetadata = () => {\r\n      if (initialTime > 0) {\r\n        video.currentTime = initialTime;\r\n      }\r\n    };\r\n\r\n    const handleLoadStart = () => {\r\n      // Video load started\r\n    };\r\n\r\n    const handleCanPlay = () => {\r\n      // Video can play\r\n    };\r\n\r\n    const handleCanPlayThrough = () => {\r\n      // Video can play through\r\n    };\r\n\r\n    const handleError = (e: Event) => {\r\n      console.error('Video playback error:', video.error?.message || 'Unknown error');\r\n    };\r\n\r\n    const handleWaiting = () => {\r\n      // Video waiting for data\r\n    };\r\n\r\n    const handleStalled = () => {\r\n      // Video stalled\r\n    };\r\n\r\n    video.addEventListener('timeupdate', handleTimeUpdate);\r\n    video.addEventListener('progress', handleProgress);\r\n    video.addEventListener('loadedmetadata', handleLoadedMetadata);\r\n    video.addEventListener('loadstart', handleLoadStart);\r\n    video.addEventListener('canplay', handleCanPlay);\r\n    video.addEventListener('canplaythrough', handleCanPlayThrough);\r\n    video.addEventListener('error', handleError);\r\n    video.addEventListener('waiting', handleWaiting);\r\n    video.addEventListener('stalled', handleStalled);\r\n\r\n    return () => {\r\n      console.log('🧹 Cleaning up video event listeners');\r\n      video.removeEventListener('timeupdate', handleTimeUpdate);\r\n      video.removeEventListener('progress', handleProgress);\r\n      video.removeEventListener('loadedmetadata', handleLoadedMetadata);\r\n      video.removeEventListener('loadstart', handleLoadStart);\r\n      video.removeEventListener('canplay', handleCanPlay);\r\n      video.removeEventListener('canplaythrough', handleCanPlayThrough);\r\n      video.removeEventListener('error', handleError);\r\n      video.removeEventListener('waiting', handleWaiting);\r\n      video.removeEventListener('stalled', handleStalled);\r\n    };\r\n  }, [onTimeUpdate, onProgress, initialTime]);\r\n\r\n  const jumpToTimestamp = (timestamp: string) => {\r\n    const video = videoRef.current;\r\n    if (!video) return;\r\n\r\n    const timeMatch = timestamp.match(/(\\d+):(\\d+)/);\r\n    if (timeMatch) {\r\n      const minutes = parseInt(timeMatch[1]);\r\n      const seconds = parseInt(timeMatch[2]);\r\n      const time = minutes * 60 + seconds;\r\n      video.currentTime = time;\r\n    }\r\n  };\r\n\r\n  console.log('🎨 About to render video element with src:', src);\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"relative video-player-container\">\r\n        <video\r\n          ref={videoRef}\r\n          className=\"w-full h-auto bg-black rounded-lg\"\r\n          controls\r\n          preload=\"metadata\"\r\n          style={{ maxHeight: '60vh' }}\r\n        >\r\n          <source src={src} type=\"video/mp4\" />\r\n          <p className=\"text-white p-4\">\r\n            Your browser does not support the video tag. \r\n            <a href={src} download className=\"text-blue-400 hover:text-blue-300 ml-2\">\r\n              Download video instead\r\n            </a>\r\n          </p>\r\n        </video>\r\n      </div>\r\n\r\n      {analysis && (\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n          <div className=\"space-y-4\">\r\n            <div\r\n              className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg cursor-pointer hover:border-white/40 transition-all\"\r\n              onClick={() => {\r\n                setDialogTab('overview');\r\n                setDialogOpen(true);\r\n              }}\r\n            >\r\n              <h3 className=\"text-white font-medium mb-2 flex items-center justify-between\">\r\n                Video Overview\r\n                <span className=\"text-xs text-white/60\">Click to expand</span>\r\n              </h3>\r\n              <p className=\"text-white/80 text-sm line-clamp-3\">{analysis.overview}</p>\r\n            </div>\r\n\r\n            <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n              <h3 className=\"text-white font-medium mb-2\">Quick Note</h3>\r\n              <p className=\"text-white/80 text-sm\">{analysis.quick_note}</p>\r\n            </div>\r\n\r\n            {analysis.slides && analysis.slides.length > 0 && (\r\n              <div\r\n                className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg cursor-pointer hover:border-white/40 transition-all\"\r\n                onClick={() => {\r\n                  setDialogTab('slides');\r\n                  setDialogOpen(true);\r\n                }}\r\n              >\r\n                <h3 className=\"text-white font-medium mb-3 flex items-center justify-between\">\r\n                  Key Slides\r\n                  <span className=\"text-xs text-white/60\">Click to expand</span>\r\n                </h3>\r\n                <div className=\"space-y-3 max-h-60 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.slides.slice(0, 2).map((slide, index) => (\r\n                    <div key={index} className=\"border-l-2 border-blue-400 pl-3 py-2\">\r\n                      <h4 className=\"text-white text-sm font-medium\">{slide.title}</h4>\r\n                      <p className=\"text-white/70 text-xs mt-1 line-clamp-2\">{slide.content}</p>\r\n                    </div>\r\n                  ))}\r\n                  {analysis.slides.length > 2 && (\r\n                    <p className=\"text-white/60 text-xs text-center mt-2\">\r\n                      +{analysis.slides.length - 2} more slides...\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            {analysis.concept_map && (\r\n              <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n                <h3\r\n                  className=\"text-white font-medium mb-3 flex items-center justify-between cursor-pointer hover:text-white/80\"\r\n                  onClick={() => {\r\n                    setDialogTab('concept_map');\r\n                    setDialogOpen(true);\r\n                  }}\r\n                >\r\n                  Concept Map\r\n                  <span className=\"text-xs text-white/60\">Click to expand</span>\r\n                </h3>\r\n                <ConceptMap\r\n                  conceptMap={analysis.concept_map}\r\n                  onOpenDialog={() => {\r\n                    setDialogTab('concept_map');\r\n                    setDialogOpen(true);\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {analysis.glossary && analysis.glossary.length > 0 && (\r\n              <div\r\n                className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg cursor-pointer hover:border-white/40 transition-all\"\r\n                onClick={() => {\r\n                  setDialogTab('glossary');\r\n                  setDialogOpen(true);\r\n                }}\r\n              >\r\n                <h3 className=\"text-white font-medium mb-3 flex items-center justify-between\">\r\n                  Glossary\r\n                  <span className=\"text-xs text-white/60\">Click to expand</span>\r\n                </h3>\r\n                <div className=\"space-y-2 max-h-60 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.glossary.slice(0, 3).map((item, index) => (\r\n                    <div key={index} className=\"p-3 bg-white/5 rounded-lg\">\r\n                      <h4 className=\"text-white font-medium text-sm\">{item.term}</h4>\r\n                      <p className=\"text-white/70 text-xs mt-1 line-clamp-2\">{item.definition}</p>\r\n                    </div>\r\n                  ))}\r\n                  {analysis.glossary.length > 3 && (\r\n                    <p className=\"text-white/60 text-xs text-center mt-2\">\r\n                      +{analysis.glossary.length - 3} more terms...\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            {analysis.cheat_sheet && analysis.cheat_sheet.length > 0 && (\r\n              <div\r\n                className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg cursor-pointer hover:border-white/40 transition-all\"\r\n                onClick={() => {\r\n                  setDialogTab('cheat_sheet');\r\n                  setDialogOpen(true);\r\n                }}\r\n              >\r\n                <h3 className=\"text-white font-medium mb-3 flex items-center justify-between\">\r\n                  Quick Reference\r\n                  <span className=\"text-xs text-white/60\">Click to expand</span>\r\n                </h3>\r\n                <div className=\"space-y-2 max-h-40 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.cheat_sheet.slice(0, 3).map((item, index) => (\r\n                    <div key={index} className=\"text-white/80 text-xs p-2 bg-white/5 rounded line-clamp-2\">\r\n                      {item}\r\n                    </div>\r\n                  ))}\r\n                  {analysis.cheat_sheet.length > 3 && (\r\n                    <p className=\"text-white/60 text-xs text-center mt-2\">\r\n                      +{analysis.cheat_sheet.length - 3} more items...\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {analysis.transcript && (\r\n              <div\r\n                className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg cursor-pointer hover:border-white/40 transition-all\"\r\n                onClick={() => {\r\n                  setDialogTab('transcript');\r\n                  setDialogOpen(true);\r\n                }}\r\n              >\r\n                <h3 className=\"text-white font-medium mb-3 flex items-center justify-between\">\r\n                  Transcript Timeline\r\n                  <span className=\"text-xs text-white/60\">Click to expand</span>\r\n                </h3>\r\n                <div className=\"space-y-2 max-h-60 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.transcript.split('[').filter(Boolean).slice(0, 10).map((section, index) => {\r\n                    const timestampMatch = section.match(/^(\\d+:\\d+)\\]/);\r\n                    if (timestampMatch) {\r\n                      const timestamp = timestampMatch[1];\r\n                      const text = section.replace(/^\\d+:\\d+\\]/, '').trim();\r\n                      \r\n                      return (\r\n                        <div \r\n                          key={index} \r\n                          className=\"flex items-start space-x-2 cursor-pointer hover:bg-white/5 p-2 rounded\"\r\n                          onClick={() => jumpToTimestamp(timestamp)}\r\n                        >\r\n                          <span className=\"text-blue-400 text-xs font-mono min-w-[40px]\">\r\n                            {timestamp}\r\n                          </span>\r\n                          <span className=\"text-white/80 text-xs\">\r\n                            {text.substring(0, 100)}...\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    }\r\n                    return null;\r\n                  })}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAee,SAAS,kBAAkB,EACxC,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,cAAc,CAAC,EACQ;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqF;IAE9H,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;YACV;QACF;QAEA,MAAM,mBAAmB;YACvB,eAAe,MAAM,WAAW;QAClC;QAEA,MAAM,iBAAiB;YACrB,IAAI,MAAM,QAAQ,GAAG,GAAG;gBACtB,MAAM,WAAW,AAAC,MAAM,WAAW,GAAG,MAAM,QAAQ,GAAI;gBACxD,aAAa;YACf;QACF;QAEA,MAAM,uBAAuB;YAC3B,IAAI,cAAc,GAAG;gBACnB,MAAM,WAAW,GAAG;YACtB;QACF;QAEA,MAAM,kBAAkB;QACtB,qBAAqB;QACvB;QAEA,MAAM,gBAAgB;QACpB,iBAAiB;QACnB;QAEA,MAAM,uBAAuB;QAC3B,yBAAyB;QAC3B;QAEA,MAAM,cAAc,CAAC;YACnB,QAAQ,KAAK,CAAC,yBAAyB,MAAM,KAAK,EAAE,WAAW;QACjE;QAEA,MAAM,gBAAgB;QACpB,yBAAyB;QAC3B;QAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAClB;QAEA,MAAM,gBAAgB,CAAC,cAAc;QACrC,MAAM,gBAAgB,CAAC,YAAY;QACnC,MAAM,gBAAgB,CAAC,kBAAkB;QACzC,MAAM,gBAAgB,CAAC,aAAa;QACpC,MAAM,gBAAgB,CAAC,WAAW;QAClC,MAAM,gBAAgB,CAAC,kBAAkB;QACzC,MAAM,gBAAgB,CAAC,SAAS;QAChC,MAAM,gBAAgB,CAAC,WAAW;QAClC,MAAM,gBAAgB,CAAC,WAAW;QAElC,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,MAAM,mBAAmB,CAAC,cAAc;YACxC,MAAM,mBAAmB,CAAC,YAAY;YACtC,MAAM,mBAAmB,CAAC,kBAAkB;YAC5C,MAAM,mBAAmB,CAAC,aAAa;YACvC,MAAM,mBAAmB,CAAC,WAAW;YACrC,MAAM,mBAAmB,CAAC,kBAAkB;YAC5C,MAAM,mBAAmB,CAAC,SAAS;YACnC,MAAM,mBAAmB,CAAC,WAAW;YACrC,MAAM,mBAAmB,CAAC,WAAW;QACvC;IACF,GAAG;QAAC;QAAc;QAAY;KAAY;IAE1C,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,YAAY,UAAU,KAAK,CAAC;QAClC,IAAI,WAAW;YACb,MAAM,UAAU,SAAS,SAAS,CAAC,EAAE;YACrC,MAAM,UAAU,SAAS,SAAS,CAAC,EAAE;YACrC,MAAM,OAAO,UAAU,KAAK;YAC5B,MAAM,WAAW,GAAG;QACtB;IACF;IAEA,QAAQ,GAAG,CAAC,8CAA8C;IAE1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK;oBACL,WAAU;oBACV,QAAQ;oBACR,SAAQ;oBACR,OAAO;wBAAE,WAAW;oBAAO;;sCAE3B,8OAAC;4BAAO,KAAK;4BAAK,MAAK;;;;;;sCACvB,8OAAC;4BAAE,WAAU;;gCAAiB;8CAE5B,8OAAC;oCAAE,MAAM;oCAAK,QAAQ;oCAAC,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;YAO/E,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS;oCACP,aAAa;oCACb,cAAc;gCAChB;;kDAEA,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;kDAAsC,SAAS,QAAQ;;;;;;;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAE,WAAU;kDAAyB,SAAS,UAAU;;;;;;;;;;;;4BAG1D,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,mBAC3C,8OAAC;gCACC,WAAU;gCACV,SAAS;oCACP,aAAa;oCACb,cAAc;gCAChB;;kDAEA,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAG,WAAU;sEAAkC,MAAM,KAAK;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAA2C,MAAM,OAAO;;;;;;;mDAF7D;;;;;4CAKX,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,8OAAC;gDAAE,WAAU;;oDAAyC;oDAClD,SAAS,MAAM,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,WAAW,kBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS;4CACP,aAAa;4CACb,cAAc;wCAChB;;4CACD;0DAEC,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC,iJAAA,CAAA,UAAU;wCACT,YAAY,SAAS,WAAW;wCAChC,cAAc;4CACZ,aAAa;4CACb,cAAc;wCAChB;;;;;;;;;;;;4BAKL,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,8OAAC;gCACC,WAAU;gCACV,SAAS;oCACP,aAAa;oCACb,cAAc;gCAChB;;kDAEA,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACxC,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAG,WAAU;sEAAkC,KAAK,IAAI;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAA2C,KAAK,UAAU;;;;;;;mDAF/D;;;;;4CAKX,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC1B,8OAAC;gDAAE,WAAU;;oDAAyC;oDAClD,SAAS,QAAQ,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,mBACrD,8OAAC;gCACC,WAAU;gCACV,SAAS;oCACP,aAAa;oCACb,cAAc;gCAChB;;kDAEA,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC3C,8OAAC;oDAAgB,WAAU;8DACxB;mDADO;;;;;4CAIX,SAAS,WAAW,CAAC,MAAM,GAAG,mBAC7B,8OAAC;gDAAE,WAAU;;oDAAyC;oDAClD,SAAS,WAAW,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;4BAO3C,SAAS,UAAU,kBAClB,8OAAC;gCACC,WAAU;gCACV,SAAS;oCACP,aAAa;oCACb,cAAc;gCAChB;;kDAEA,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS;4CACzE,MAAM,iBAAiB,QAAQ,KAAK,CAAC;4CACrC,IAAI,gBAAgB;gDAClB,MAAM,YAAY,cAAc,CAAC,EAAE;gDACnC,MAAM,OAAO,QAAQ,OAAO,CAAC,cAAc,IAAI,IAAI;gDAEnD,qBACE,8OAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,gBAAgB;;sEAE/B,8OAAC;4DAAK,WAAU;sEACb;;;;;;sEAEH,8OAAC;4DAAK,WAAU;;gEACb,KAAK,SAAS,CAAC,GAAG;gEAAK;;;;;;;;mDARrB;;;;;4CAYX;4CACA,OAAO;wCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/document/DocumentViewer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Download, FileText, Eye, Check } from 'lucide-react';\r\nimport { CourseFile, UserProgress } from '@/types/course';\r\n\r\ninterface DocumentViewerProps {\r\n  file: CourseFile;\r\n  userProgress?: UserProgress | null;\r\n  onProgressUpdate?: (progress: UserProgress) => void;\r\n}\r\n\r\nexport default function DocumentViewer({ file, userProgress, onProgressUpdate }: DocumentViewerProps) {\r\n  const [content, setContent] = useState<string>('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n  const [extractedText, setExtractedText] = useState<string>('');\r\n  const [isExtracting, setIsExtracting] = useState(false);\r\n\r\n  const loadDocumentContent = async () => {\r\n    // Always try to extract text for better viewing\r\n    setIsExtracting(true);\r\n    setError('');\r\n    \r\n    try {\r\n      const response = await fetch('/api/extract-text', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ filePath: file.path })\r\n      });\r\n      \r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.success) {\r\n          setExtractedText(result.content);\r\n          \r\n          // For .txt files, also load the original content\r\n          if (file.extension === '.txt') {\r\n            setIsLoading(true);\r\n            try {\r\n              const fileResponse = await fetch(`/api/serve-file?path=${encodeURIComponent(file.path)}`);\r\n              if (fileResponse.ok) {\r\n                const text = await fileResponse.text();\r\n                setContent(text);\r\n              }\r\n            } catch (err) {\r\n              console.error('Error loading original text file:', err);\r\n            } finally {\r\n              setIsLoading(false);\r\n            }\r\n          }\r\n        } else {\r\n          setError('Failed to extract document content');\r\n        }\r\n      } else {\r\n        setError('Failed to extract document content');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error extracting text:', err);\r\n      setError('Error extracting document content');\r\n    } finally {\r\n      setIsExtracting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadDocumentContent();\r\n  }, [file.path]);\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    const mb = bytes / (1024 * 1024);\r\n    if (mb > 1000) return `${(mb / 1024).toFixed(1)} GB`;\r\n    return `${mb.toFixed(1)} MB`;\r\n  };\r\n\r\n  const downloadFile = async () => {\r\n    try {\r\n      const response = await fetch(`/api/serve-file?path=${encodeURIComponent(file.path)}`);\r\n      if (!response.ok) {\r\n        throw new Error('Download failed');\r\n      }\r\n      \r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = file.name;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (err) {\r\n      console.error('Download error:', err);\r\n      setError('Failed to download file');\r\n    }\r\n  };\r\n\r\n  const isCompleted = userProgress?.viewedDocuments?.includes(file.path) || false;\r\n\r\n  const toggleCompletion = () => {\r\n    if (!userProgress || !onProgressUpdate) return;\r\n\r\n    const viewedDocuments = userProgress.viewedDocuments || [];\r\n    const updatedViewedDocuments = isCompleted\r\n      ? viewedDocuments.filter(path => path !== file.path)\r\n      : [...viewedDocuments, file.path];\r\n\r\n    const updatedProgress = {\r\n      ...userProgress,\r\n      viewedDocuments: updatedViewedDocuments,\r\n      lastAccessed: new Date().toISOString()\r\n    };\r\n\r\n    onProgressUpdate(updatedProgress);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <FileText size={24} className=\"text-blue-400\" />\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">{file.name}</h2>\r\n            <p className=\"text-white/60 text-sm\">\r\n              {file.fileType.toUpperCase()} • {formatFileSize(file.size)}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {userProgress && onProgressUpdate && (\r\n            <Button\r\n              onClick={toggleCompletion}\r\n              variant={isCompleted ? \"default\" : \"outline\"}\r\n              className={`${\r\n                isCompleted\r\n                  ? \"bg-green-500 hover:bg-green-600 text-white\"\r\n                  : \"border-white/20 text-white hover:bg-white/10\"\r\n              }`}\r\n            >\r\n              <Check size={16} className=\"mr-2\" />\r\n              {isCompleted ? \"Completed\" : \"Mark Complete\"}\r\n            </Button>\r\n          )}\r\n          {file.extension === '.pdf' && (\r\n            <Button\r\n              onClick={() => window.open(`/api/serve-file?path=${encodeURIComponent(file.path)}`, '_blank')}\r\n              variant=\"outline\"\r\n              className=\"border-white/20 text-white hover:bg-white/10\"\r\n            >\r\n              <Eye size={16} className=\"mr-2\" />\r\n              View PDF\r\n            </Button>\r\n          )}\r\n          <Button\r\n            onClick={downloadFile}\r\n            variant=\"outline\"\r\n            className=\"border-white/20 text-white hover:bg-white/10\"\r\n          >\r\n            <Download size={16} className=\"mr-2\" />\r\n            Download\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"glass-morphism-dark p-6 rounded-lg\">\r\n        {(isLoading || isExtracting) && (\r\n          <div className=\"flex items-center justify-center py-8\">\r\n            <div className=\"animate-spin w-6 h-6 border-2 border-white/30 border-t-white rounded-full\"></div>\r\n            <span className=\"ml-3 text-white/70\">\r\n              {isExtracting ? 'Extracting text...' : 'Loading document...'}\r\n            </span>\r\n          </div>\r\n        )}\r\n        \r\n        {error && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-white/30\" />\r\n            <p className=\"text-white/70 mb-2\">Unable to preview this document</p>\r\n            <p className=\"text-white/50 text-sm\">{error}</p>\r\n          </div>\r\n        )}\r\n        \r\n        {extractedText && (\r\n          <div className=\"text-white/80\">\r\n            <h3 className=\"text-lg font-medium mb-4 text-white\">Document Content</h3>\r\n            <div className=\"text-sm leading-relaxed bg-black/20 p-4 rounded-lg\">\r\n              {file.extension === '.txt' && content ? (\r\n                <pre className=\"whitespace-pre-wrap font-mono\">{content}</pre>\r\n              ) : (\r\n                <div className=\"whitespace-pre-wrap\">{extractedText}</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {file.extension === '.pdf' && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-blue-400\" />\r\n            <p className=\"text-white/80 mb-2\">PDF Document</p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              Click \"View PDF\" to open in a new tab or download to view locally.\r\n            </p>\r\n            <div className=\"flex justify-center space-x-4\">\r\n              <Button\r\n                onClick={() => window.open(`/api/serve-file?path=${encodeURIComponent(file.path)}`, '_blank')}\r\n                className=\"bg-blue-500 hover:bg-blue-600\"\r\n              >\r\n                <Eye size={16} className=\"mr-2\" />\r\n                View in Browser\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {file.extension === '.docx' && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-green-400\" />\r\n            <p className=\"text-white/80 mb-2\">Microsoft Word Document</p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              {file.name} ({formatFileSize(file.size)})\r\n            </p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              This document contains course materials. Download to view the full content.\r\n            </p>\r\n            <div className=\"flex justify-center space-x-4\">\r\n              <Button\r\n                onClick={downloadFile}\r\n                className=\"bg-green-500 hover:bg-green-600\"\r\n              >\r\n                <Download size={16} className=\"mr-2\" />\r\n                Download Document\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {!extractedText && !isLoading && !isExtracting && !error && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-white/30\" />\r\n            <p className=\"text-white/70 mb-2\">{file.name}</p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              {file.fileType.toUpperCase()} file ({formatFileSize(file.size)})\r\n            </p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              This file type cannot be previewed in the browser. Download to view on your computer.\r\n            </p>\r\n            <Button\r\n              onClick={downloadFile}\r\n              variant=\"outline\"\r\n              className=\"border-white/20 text-white hover:bg-white/10\"\r\n            >\r\n              <Download size={16} className=\"mr-2\" />\r\n              Download File\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAae,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAuB;IAClG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,sBAAsB;QAC1B,gDAAgD;QAChD,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU,KAAK,IAAI;gBAAC;YAC7C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,IAAI,OAAO,OAAO,EAAE;oBAClB,iBAAiB,OAAO,OAAO;oBAE/B,iDAAiD;oBACjD,IAAI,KAAK,SAAS,KAAK,QAAQ;wBAC7B,aAAa;wBACb,IAAI;4BACF,MAAM,eAAe,MAAM,MAAM,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG;4BACxF,IAAI,aAAa,EAAE,EAAE;gCACnB,MAAM,OAAO,MAAM,aAAa,IAAI;gCACpC,WAAW;4BACb;wBACF,EAAE,OAAO,KAAK;4BACZ,QAAQ,KAAK,CAAC,qCAAqC;wBACrD,SAAU;4BACR,aAAa;wBACf;oBACF;gBACF,OAAO;oBACL,SAAS;gBACX;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,KAAK,IAAI;KAAC;IAEd,MAAM,iBAAiB,CAAC;QACtB,MAAM,KAAK,QAAQ,CAAC,OAAO,IAAI;QAC/B,IAAI,KAAK,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QACpD,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;IAC9B;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG;YACpF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,KAAK,IAAI;YACzB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS;QACX;IACF;IAEA,MAAM,cAAc,cAAc,iBAAiB,SAAS,KAAK,IAAI,KAAK;IAE1E,MAAM,mBAAmB;QACvB,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;QAExC,MAAM,kBAAkB,aAAa,eAAe,IAAI,EAAE;QAC1D,MAAM,yBAAyB,cAC3B,gBAAgB,MAAM,CAAC,CAAA,OAAQ,SAAS,KAAK,IAAI,IACjD;eAAI;YAAiB,KAAK,IAAI;SAAC;QAEnC,MAAM,kBAAkB;YACtB,GAAG,YAAY;YACf,iBAAiB;YACjB,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC,KAAK,IAAI;;;;;;kDAC3D,8OAAC;wCAAE,WAAU;;4CACV,KAAK,QAAQ,CAAC,WAAW;4CAAG;4CAAI,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;;kCAI/D,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,kCACf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAS,cAAc,YAAY;gCACnC,WAAW,GACT,cACI,+CACA,gDACJ;;kDAEF,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAC1B,cAAc,cAAc;;;;;;;4BAGhC,KAAK,SAAS,KAAK,wBAClB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG,EAAE;gCACpF,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;0CAItC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,aAAa,YAAY,mBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,eAAe,uBAAuB;;;;;;;;;;;;oBAK5C,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;oBAIzC,+BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,KAAK,UAAU,wBAC5B,8OAAC;oCAAI,WAAU;8CAAiC;;;;;yDAEhD,8OAAC;oCAAI,WAAU;8CAAuB;;;;;;;;;;;;;;;;;oBAM7C,KAAK,SAAS,KAAK,wBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG,EAAE;oCACpF,WAAU;;sDAEV,8OAAC,gMAAA,CAAA,MAAG;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;oBAOzC,KAAK,SAAS,KAAK,yBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCAAE,WAAU;;oCACV,KAAK,IAAI;oCAAC;oCAAG,eAAe,KAAK,IAAI;oCAAE;;;;;;;0CAE1C,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;oBAO9C,CAAC,iBAAiB,CAAC,aAAa,CAAC,gBAAgB,CAAC,uBACjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAsB,KAAK,IAAI;;;;;;0CAC5C,8OAAC;gCAAE,WAAU;;oCACV,KAAK,QAAQ,CAAC,WAAW;oCAAG;oCAAQ,eAAe,KAAK,IAAI;oCAAE;;;;;;;0CAEjE,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 1610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/app/course/%5B...module%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { useParams, useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Input } from '@/components/ui/input';\r\nimport CustomVideoPlayer from '@/components/video/CustomVideoPlayer';\r\nimport DocumentViewer from '@/components/document/DocumentViewer';\r\nimport { \r\n  ArrowLeft, \r\n  FileText, \r\n  PlayCircle, \r\n  Image as ImageIcon, \r\n  Download,\r\n  Search,\r\n  ChevronRight,\r\n  ChevronDown,\r\n  StickyNote,\r\n  Users,\r\n  BarChart,\r\n  Clock,\r\n  Target,\r\n  Folder,\r\n  FolderOpen\r\n} from 'lucide-react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { CourseStructure, CourseFile, CoursePart, UserProgress, VideoAnalysis } from '@/types/course';\r\nimport { useHotkeys } from 'react-hotkeys-hook';\r\n\r\nexport default function ModulePage() {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const [courseStructure, setCourseStructure] = useState<CourseStructure | null>(null);\r\n  const [currentModule, setCurrentModule] = useState<CoursePart | null>(null);\r\n  const [selectedPart, setSelectedPart] = useState<CourseFile | null>(null);\r\n  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [expandedParts, setExpandedParts] = useState<Set<string>>(new Set());\r\n  const [videoAnalysis, setVideoAnalysis] = useState<VideoAnalysis | null>(null);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [notes, setNotes] = useState<string>('');\r\n  const [showNotes, setShowNotes] = useState(false);\r\n  const [currentPath, setCurrentPath] = useState<string[]>([]);\r\n  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());\r\n  const notesRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  const modulePath = decodeURIComponent(Array.isArray(params.module) ? params.module.join('/') : params.module || '');\r\n\r\n  // Keyboard shortcuts\r\n  useHotkeys('ctrl+/', () => setShowNotes(!showNotes));\r\n  useHotkeys('ctrl+b', () => setSidebarCollapsed(!sidebarCollapsed));\r\n  useHotkeys('escape', () => setSelectedPart(null));\r\n\r\n  useEffect(() => {\r\n    loadCourseStructure();\r\n    loadUserProgress();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (courseStructure && modulePath) {\r\n      findModule();\r\n    }\r\n  }, [courseStructure, modulePath]);\r\n\r\n  useEffect(() => {\r\n    if (selectedPart?.isVideo) {\r\n      loadExistingAnalysis(selectedPart.path);\r\n    } else {\r\n      setVideoAnalysis(null);\r\n    }\r\n  }, [selectedPart]);\r\n\r\n  const loadCourseStructure = async () => {\r\n    try {\r\n      const response = await fetch('/api/course-structure');\r\n      const data = await response.json();\r\n      setCourseStructure(data);\r\n    } catch (error) {\r\n      console.error('Failed to load course structure:', error);\r\n    }\r\n  };\r\n\r\n  const loadUserProgress = async () => {\r\n    const auth = localStorage.getItem('courseAuth');\r\n    if (auth) {\r\n      const { username } = JSON.parse(auth);\r\n      try {\r\n        const response = await fetch(`/api/user-progress?username=${encodeURIComponent(username)}`);\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.success) {\r\n            setUserProgress(data.progress);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load user progress from server:', error);\r\n        // Fallback to localStorage\r\n        const savedProgress = localStorage.getItem(`progress_${username}`);\r\n        if (savedProgress) {\r\n          setUserProgress(JSON.parse(savedProgress));\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  const saveUserProgress = async (updatedProgress: UserProgress) => {\r\n    try {\r\n      const response = await fetch('/api/user-progress', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          username: updatedProgress.username,\r\n          progress: updatedProgress\r\n        })\r\n      });\r\n      \r\n      if (response.ok) {\r\n        // Also save to localStorage as backup\r\n        localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));\r\n      } else {\r\n        console.error('Failed to save progress to server');\r\n        // Fallback to localStorage only\r\n        localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving progress:', error);\r\n      // Fallback to localStorage only\r\n      localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));\r\n    }\r\n  };\r\n\r\n  const findModule = () => {\r\n    if (!courseStructure) return;\r\n\r\n    console.log('🔍 Finding module for path:', modulePath);\r\n    console.log('📚 Available modules:', courseStructure.modules.map(m => ({ name: m.name, path: m.path, partsCount: m.parts?.length || 0 })));\r\n\r\n    const findModuleRecursive = (modules: CoursePart[]): CoursePart | null => {\r\n      for (const module of modules) {\r\n        console.log('🔎 Checking module:', module.name, 'path:', module.path, 'vs modulePath:', modulePath);\r\n        if (module.path === modulePath) {\r\n          console.log('✅ Found matching module:', module.name, 'with', module.parts?.length || 0, 'parts');\r\n          return module;\r\n        }\r\n        if (module.type === 'module' && module.parts) {\r\n          const found = findModuleRecursive(module.parts as CoursePart[]);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    };\r\n\r\n    const found = findModuleRecursive(courseStructure.modules);\r\n    console.log('🎯 Final result:', found ? `Found ${found.name} with ${found.parts?.length || 0} parts` : 'No module found');\r\n    setCurrentModule(found);\r\n    \r\n    // Auto-select first available part if no part is selected\r\n    if (found && !selectedPart) {\r\n      const firstPart = findFirstPart(found.parts);\r\n      if (firstPart) {\r\n        setSelectedPart(firstPart);\r\n      }\r\n    }\r\n  };\r\n\r\n  const findFirstPart = (parts: (CourseFile | CoursePart)[]): CourseFile | null => {\r\n    // First, look for documents\r\n    for (const part of parts) {\r\n      if (part.type === 'file' && (part as CourseFile).isDocument) {\r\n        return part as CourseFile;\r\n      }\r\n    }\r\n\r\n    // Then look for videos if no documents found\r\n    for (const part of parts) {\r\n      if (part.type === 'file' && (part as CourseFile).isVideo) {\r\n        return part as CourseFile;\r\n      }\r\n    }\r\n\r\n    // Finally, look for any other files\r\n    for (const part of parts) {\r\n      if (part.type === 'file') {\r\n        return part as CourseFile;\r\n      }\r\n    }\r\n\r\n    // Recursively search in folders/modules\r\n    for (const part of parts) {\r\n      if (part.type === 'folder' || part.type === 'module') {\r\n        const found = findFirstPart((part as CoursePart).parts);\r\n        if (found) return found;\r\n      }\r\n    }\r\n\r\n    return null;\r\n  };\r\n\r\n  const togglePartExpansion = (partName: string) => {\r\n    const newExpanded = new Set(expandedParts);\r\n    if (newExpanded.has(partName)) {\r\n      newExpanded.delete(partName);\r\n    } else {\r\n      newExpanded.add(partName);\r\n    }\r\n    setExpandedParts(newExpanded);\r\n  };\r\n\r\n  const loadExistingAnalysis = async (videoPath: string) => {\r\n    // Clear previous analysis first\r\n    setVideoAnalysis(null);\r\n    setIsAnalyzing(false);\r\n    \r\n    try {\r\n      const response = await fetch('/api/analyze-video', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ videoPath, checkOnly: true })\r\n      });\r\n      if (response.ok) {\r\n        const analysis = await response.json();\r\n        setVideoAnalysis(analysis);\r\n      } else {\r\n        // No existing analysis found, keep videoAnalysis as null\r\n        console.log('No existing analysis found for this video');\r\n      }\r\n    } catch (error) {\r\n      console.log('No existing analysis found for this video');\r\n      // Keep videoAnalysis as null if there's an error\r\n    }\r\n  };\r\n\r\n  const analyzeVideo = async (videoPath: string) => {\r\n    setIsAnalyzing(true);\r\n    try {\r\n      const response = await fetch('/api/analyze-video', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ videoPath })\r\n      });\r\n      const analysis = await response.json();\r\n      setVideoAnalysis(analysis);\r\n    } catch (error) {\r\n      console.error('Failed to analyze video:', error);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  const saveNote = () => {\r\n    if (!userProgress || !notes.trim()) return;\r\n\r\n    const newNote = {\r\n      id: Date.now().toString(),\r\n      content: notes.trim(),\r\n      timestamp: new Date().toISOString(),\r\n      videoPath: selectedPart?.path,\r\n      videoTime: 0 // TODO: Get current video time\r\n    };\r\n\r\n    const updatedProgress = {\r\n      ...userProgress,\r\n      notes: [...userProgress.notes, newNote]\r\n    };\r\n\r\n    setUserProgress(updatedProgress);\r\n    localStorage.setItem(`progress_${userProgress.username}`, JSON.stringify(updatedProgress));\r\n    setNotes('');\r\n  };\r\n\r\n  const getFileIcon = (file: CourseFile) => {\r\n    if (file.isVideo) return <PlayCircle size={16} className=\"text-red-400\" />;\r\n    if (file.isDocument) return <FileText size={16} className=\"text-blue-400\" />;\r\n    if (file.isImage) return <ImageIcon size={16} className=\"text-green-400\" />;\r\n    return <FileText size={16} className=\"text-gray-400\" />;\r\n  };\r\n\r\n  const getFolderIcon = (path: string, isExpanded: boolean) => {\r\n    return isExpanded ? \r\n      <FolderOpen size={16} className=\"text-yellow-400\" /> : \r\n      <Folder size={16} className=\"text-yellow-400\" />;\r\n  };\r\n\r\n  const toggleFolder = (folderPath: string) => {\r\n    const newExpanded = new Set(expandedFolders);\r\n    if (newExpanded.has(folderPath)) {\r\n      newExpanded.delete(folderPath);\r\n    } else {\r\n      newExpanded.add(folderPath);\r\n    }\r\n    setExpandedFolders(newExpanded);\r\n  };\r\n\r\n  const navigateToFolder = (folderPath: string) => {\r\n    const pathParts = folderPath.split('/');\r\n    setCurrentPath(pathParts);\r\n  };\r\n\r\n  const navigateBack = () => {\r\n    if (currentPath.length > 0) {\r\n      setCurrentPath(currentPath.slice(0, -1));\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    const mb = bytes / (1024 * 1024);\r\n    if (mb > 1000) return `${(mb / 1024).toFixed(1)} GB`;\r\n    return `${mb.toFixed(1)} MB`;\r\n  };\r\n\r\n  const getCurrentParts = () => {\r\n    console.log('📂 getCurrentParts called');\r\n    console.log('📂 currentModule:', currentModule ? `${currentModule.name} with ${currentModule.parts?.length || 0} parts` : 'null');\r\n    console.log('📂 currentPath:', currentPath);\r\n\r\n    if (!currentModule) {\r\n      console.log('❌ No currentModule, returning empty array');\r\n      return [];\r\n    }\r\n\r\n    let currentParts = currentModule.parts;\r\n    console.log('📂 Initial currentParts:', currentParts?.length || 0, 'items');\r\n\r\n    // Navigate through the path to get current folder contents\r\n    for (const pathPart of currentPath) {\r\n      console.log('📂 Navigating to path part:', pathPart);\r\n      const folder = currentParts.find(p => p.name === pathPart && p.type === 'folder');\r\n      if (folder && 'parts' in folder && folder.parts) {\r\n        currentParts = folder.parts;\r\n        console.log('📂 Found folder, now have', currentParts.length, 'parts');\r\n      } else {\r\n        console.log('❌ Folder not found, returning empty array');\r\n        return [];\r\n      }\r\n    }\r\n\r\n    console.log('📂 Final currentParts:', currentParts?.length || 0, 'items');\r\n    return currentParts || [];\r\n  };\r\n\r\n  const filteredParts = getCurrentParts().filter(part => \r\n    part.name.toLowerCase().includes(searchQuery.toLowerCase())\r\n  );\r\n\r\n  const renderPartsList = (parts: (CourseFile | CoursePart)[], level: number = 0) => {\r\n    return parts.map((part, index) => {\r\n      const isFile = part.type === 'file';\r\n      const isFolder = part.type === 'folder';\r\n      const file = part as CourseFile;\r\n      const isSelected = selectedPart?.path === part.path;\r\n      const isExpanded = expandedFolders.has(part.path);\r\n      const indent = level * 20;\r\n\r\n      // Check if document is completed\r\n      const isDocumentCompleted = isFile && file.isDocument && userProgress?.viewedDocuments?.includes(file.path);\r\n      const isVideoWatched = isFile && file.isVideo && userProgress?.watchedVideos?.includes(file.path);\r\n      \r\n      return (\r\n        <div key={part.path}>\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ delay: index * 0.05 }}\r\n            className={`p-3 rounded-lg border cursor-pointer transition-all ${\r\n              isSelected\r\n                ? 'bg-blue-500/20 border-blue-400/50'\r\n                : isDocumentCompleted || isVideoWatched\r\n                ? 'bg-gray-500/20 border-gray-400/30 hover:bg-gray-500/30'\r\n                : 'bg-white/5 border-white/10 hover:bg-white/10'\r\n            }`}\r\n            style={{ marginLeft: `${indent}px` }}\r\n            onClick={() => {\r\n              if (isFile) {\r\n                setSelectedPart(file);\r\n              } else if (isFolder) {\r\n                toggleFolder(part.path);\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center space-x-3\">\r\n              {isFile && getFileIcon(file)}\r\n              {isFolder && getFolderIcon(part.path, isExpanded)}\r\n              <div className=\"flex-1 min-w-0\">\r\n                {!sidebarCollapsed && (\r\n                  <>\r\n                    <p className=\"text-white text-sm font-medium truncate\">\r\n                      {part.name.replace(/\\.ONEDDL\\.(mp4|docx|pdf|ts|png)$/, '')}\r\n                    </p>\r\n                    {isFile && (\r\n                      <p className=\"text-white/60 text-xs\">\r\n                        {file.fileType} • {formatFileSize(file.size)}\r\n                      </p>\r\n                    )}\r\n                    {isFolder && 'parts' in part && part.parts && (\r\n                      <p className=\"text-white/60 text-xs\">\r\n                        {part.parts.length} items\r\n                      </p>\r\n                    )}\r\n                  </>\r\n                )}\r\n              </div>\r\n              {isFolder && !sidebarCollapsed && (\r\n                <ChevronDown \r\n                  size={14} \r\n                  className={`text-white/60 transition-transform ${\r\n                    isExpanded ? 'rotate-180' : ''\r\n                  }`} \r\n                />\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n          \r\n          {isFolder && isExpanded && 'parts' in part && part.parts && (\r\n            <div className=\"mt-1\">\r\n              {renderPartsList(part.parts, level + 1)}\r\n            </div>\r\n          )}\r\n        </div>\r\n      );\r\n    });\r\n  };\r\n\r\n  const renderFileContent = () => {\r\n    if (!selectedPart) {\r\n      return (\r\n        <div className=\"flex items-center justify-center h-96 text-white/60\">\r\n          <div className=\"text-center\">\r\n            <FileText size={48} className=\"mx-auto mb-4 opacity-50\" />\r\n            <p>Select a file to view its content</p>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (selectedPart.isVideo) {\r\n      return (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-semibold text-white\">{selectedPart.name}</h2>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button \r\n                onClick={() => {\r\n                  const link = document.createElement('a');\r\n                  link.href = `/api/serve-file?path=${encodeURIComponent(selectedPart.path)}`;\r\n                  link.download = selectedPart.name;\r\n                  document.body.appendChild(link);\r\n                  link.click();\r\n                  document.body.removeChild(link);\r\n                }}\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n              >\r\n                <Download size={16} className=\"mr-2\" />\r\n                Download\r\n              </Button>\r\n              {!videoAnalysis && !isAnalyzing && (\r\n                <Button \r\n                  onClick={() => analyzeVideo(selectedPart.path)}\r\n                  variant=\"outline\"\r\n                >\r\n                  Generate AI Analysis\r\n                </Button>\r\n              )}\r\n              {videoAnalysis && (\r\n                <Button \r\n                  onClick={() => analyzeVideo(selectedPart.path)}\r\n                  variant=\"outline\"\r\n                >\r\n                  Regenerate Analysis\r\n                </Button>\r\n              )}\r\n              {isAnalyzing && (\r\n                <div className=\"flex items-center space-x-2 text-white/70\">\r\n                  <div className=\"animate-spin w-4 h-4 border-2 border-white/30 border-t-white rounded-full\"></div>\r\n                  <span className=\"text-sm\">Analyzing with AI...</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          <CustomVideoPlayer\r\n            src={`/api/serve-file?path=${encodeURIComponent(selectedPart.path)}`}\r\n            analysis={videoAnalysis || undefined}\r\n            onTimeUpdate={(time) => {\r\n              // Update user progress\r\n              if (userProgress) {\r\n                const updatedProgress = {\r\n                  ...userProgress,\r\n                  totalWatchTime: userProgress.totalWatchTime + 1,\r\n                  lastAccessed: new Date().toISOString()\r\n                };\r\n                setUserProgress(updatedProgress);\r\n                saveUserProgress(updatedProgress);\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (selectedPart.isDocument) {\r\n      return (\r\n        <DocumentViewer\r\n          file={selectedPart}\r\n          userProgress={userProgress}\r\n          onProgressUpdate={(updatedProgress) => {\r\n            setUserProgress(updatedProgress);\r\n            saveUserProgress(updatedProgress);\r\n          }}\r\n        />\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className=\"glass-morphism p-6 rounded-lg\">\r\n        <h2 className=\"text-xl font-semibold text-white mb-4\">{selectedPart.name}</h2>\r\n        <div className=\"text-white/80\">\r\n          <p>File type: {selectedPart.fileType}</p>\r\n          <p>Size: {formatFileSize(selectedPart.size)}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (!currentModule) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"glass-morphism p-8 text-center\">\r\n          <div className=\"animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4\"></div>\r\n          <p className=\"text-white\">Loading module...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex\">\r\n      {/* Sidebar */}\r\n      <motion.div\r\n        animate={{ width: sidebarCollapsed ? 80 : 400 }}\r\n        transition={{ duration: 0.3 }}\r\n        className=\"bg-black/20 backdrop-blur-md border-r border-white/10 flex flex-col\"\r\n      >\r\n        <div className=\"p-4 border-b border-white/10\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => router.push('/')}\r\n              className=\"text-white hover:bg-white/10\"\r\n            >\r\n              <ArrowLeft size={16} className=\"mr-2\" />\r\n              {!sidebarCollapsed && 'Back to Dashboard'}\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\r\n              className=\"text-white hover:bg-white/10\"\r\n            >\r\n              {sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronDown size={16} />}\r\n            </Button>\r\n          </div>\r\n          \r\n          {!sidebarCollapsed && (\r\n            <>\r\n              <h1 className=\"text-white font-semibold text-lg mb-2\">{currentModule.name}</h1>\r\n              <div className=\"relative\">\r\n                <Search size={16} className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50\" />\r\n                <Input\r\n                  placeholder=\"Search parts...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50\"\r\n                />\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-2\">\r\n          {currentPath.length > 0 && (\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={navigateBack}\r\n              className=\"w-full text-left text-white/70 hover:text-white hover:bg-white/10 mb-4\"\r\n            >\r\n              <ArrowLeft size={16} className=\"mr-2\" />\r\n              Back to {currentPath.length > 1 ? currentPath[currentPath.length - 2] : 'Module'}\r\n            </Button>\r\n          )}\r\n          \r\n          {renderPartsList(filteredParts)}\r\n          \r\n          {filteredParts.length === 0 && (\r\n            <div className=\"text-center py-8 text-white/60\">\r\n              <FileText size={48} className=\"mx-auto mb-4 opacity-50\" />\r\n              <p>No files found in this {currentPath.length > 0 ? 'folder' : 'module'}</p>\r\n              {searchQuery && (\r\n                <p className=\"text-sm mt-2\">Try adjusting your search term</p>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {!sidebarCollapsed && userProgress && (\r\n          <div className=\"p-4 border-t border-white/10\">\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center justify-between text-sm\">\r\n                <span className=\"text-white/70\">Progress</span>\r\n                <span className=\"text-white\">\r\n                  {Math.round((userProgress.watchedVideos.length / (courseStructure?.totalVideos || 1)) * 100)}%\r\n                </span>\r\n              </div>\r\n              <Progress \r\n                value={(userProgress.watchedVideos.length / (courseStructure?.totalVideos || 1)) * 100} \r\n                className=\"h-2\" \r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col\">\r\n        <div className=\"flex-1 p-6\">\r\n          {renderFileContent()}\r\n        </div>\r\n\r\n        {/* Notes Panel */}\r\n        <AnimatePresence>\r\n          {showNotes && (\r\n            <motion.div\r\n              initial={{ height: 0 }}\r\n              animate={{ height: 200 }}\r\n              exit={{ height: 0 }}\r\n              className=\"border-t border-white/10 bg-black/20 backdrop-blur-md overflow-hidden\"\r\n            >\r\n              <div className=\"p-4\">\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                  <h3 className=\"text-white font-medium flex items-center gap-2\">\r\n                    <StickyNote size={16} />\r\n                    Universal Notes\r\n                  </h3>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={saveNote}\r\n                      disabled={!notes.trim()}\r\n                      className=\"bg-blue-500 hover:bg-blue-600\"\r\n                    >\r\n                      Save Note\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"ghost\"\r\n                      onClick={() => setShowNotes(false)}\r\n                      className=\"text-white hover:bg-white/10\"\r\n                    >\r\n                      ×\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                <textarea\r\n                  ref={notesRef}\r\n                  value={notes}\r\n                  onChange={(e) => setNotes(e.target.value)}\r\n                  placeholder=\"Take notes... (Ctrl+/ to toggle)\"\r\n                  className=\"w-full h-24 bg-white/10 border border-white/20 rounded-lg p-3 text-white placeholder:text-white/50 resize-none focus:outline-none focus:border-white/40\"\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n\r\n        {/* Floating Notes Button */}\r\n        {!showNotes && (\r\n          <Button\r\n            onClick={() => setShowNotes(true)}\r\n            className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 rounded-full p-3\"\r\n            title=\"Open Notes (Ctrl+/)\"\r\n          >\r\n            <StickyNote size={20} />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAAA;AAEA;AA9BA;;;;;;;;;;;;AAgCe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACxE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAE7C,MAAM,aAAa,mBAAmB,MAAM,OAAO,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,OAAO,MAAM,IAAI;IAEhH,qBAAqB;IACrB,CAAA,GAAA,iMAAA,CAAA,aAAU,AAAD,EAAE,UAAU,IAAM,aAAa,CAAC;IACzC,CAAA,GAAA,iMAAA,CAAA,aAAU,AAAD,EAAE,UAAU,IAAM,oBAAoB,CAAC;IAChD,CAAA,GAAA,iMAAA,CAAA,aAAU,AAAD,EAAE,UAAU,IAAM,gBAAgB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,YAAY;YACjC;QACF;IACF,GAAG;QAAC;QAAiB;KAAW;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,SAAS;YACzB,qBAAqB,aAAa,IAAI;QACxC,OAAO;YACL,iBAAiB;QACnB;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,IAAI,MAAM;YACR,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,KAAK,CAAC;YAChC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,mBAAmB,WAAW;gBAC1F,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,gBAAgB,KAAK,QAAQ;oBAC/B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,2BAA2B;gBAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,UAAU;gBACjE,IAAI,eAAe;oBACjB,gBAAgB,KAAK,KAAK,CAAC;gBAC7B;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,gBAAgB,QAAQ;oBAClC,UAAU;gBACZ;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,sCAAsC;gBACtC,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,gBAAgB,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;YAC9E,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,gCAAgC;gBAChC,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,gBAAgB,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;YAC9E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gCAAgC;YAChC,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,gBAAgB,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;QAC9E;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,iBAAiB;QAEtB,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,MAAM,EAAE,IAAI;gBAAE,YAAY,EAAE,KAAK,EAAE,UAAU;YAAE,CAAC;QAEvI,MAAM,sBAAsB,CAAC;YAC3B,KAAK,MAAM,UAAU,QAAS;gBAC5B,QAAQ,GAAG,CAAC,uBAAuB,OAAO,IAAI,EAAE,SAAS,OAAO,IAAI,EAAE,kBAAkB;gBACxF,IAAI,OAAO,IAAI,KAAK,YAAY;oBAC9B,QAAQ,GAAG,CAAC,4BAA4B,OAAO,IAAI,EAAE,QAAQ,OAAO,KAAK,EAAE,UAAU,GAAG;oBACxF,OAAO;gBACT;gBACA,IAAI,OAAO,IAAI,KAAK,YAAY,OAAO,KAAK,EAAE;oBAC5C,MAAM,QAAQ,oBAAoB,OAAO,KAAK;oBAC9C,IAAI,OAAO,OAAO;gBACpB;YACF;YACA,OAAO;QACT;QAEA,MAAM,QAAQ,oBAAoB,gBAAgB,OAAO;QACzD,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG;QACvG,iBAAiB;QAEjB,0DAA0D;QAC1D,IAAI,SAAS,CAAC,cAAc;YAC1B,MAAM,YAAY,cAAc,MAAM,KAAK;YAC3C,IAAI,WAAW;gBACb,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,4BAA4B;QAC5B,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,UAAU,AAAC,KAAoB,UAAU,EAAE;gBAC3D,OAAO;YACT;QACF;QAEA,6CAA6C;QAC7C,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,UAAU,AAAC,KAAoB,OAAO,EAAE;gBACxD,OAAO;YACT;QACF;QAEA,oCAAoC;QACpC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,QAAQ;gBACxB,OAAO;YACT;QACF;QAEA,wCAAwC;QACxC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,UAAU;gBACpD,MAAM,QAAQ,cAAc,AAAC,KAAoB,KAAK;gBACtD,IAAI,OAAO,OAAO;YACpB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC7B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,iBAAiB;IACnB;IAEA,MAAM,uBAAuB,OAAO;QAClC,gCAAgC;QAChC,iBAAiB;QACjB,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW,WAAW;gBAAK;YACpD;YACA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,iBAAiB;YACnB,OAAO;gBACL,yDAAyD;gBACzD,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACZ,iDAAiD;QACnD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YACA,MAAM,WAAW,MAAM,SAAS,IAAI;YACpC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,IAAI;QAEpC,MAAM,UAAU;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,MAAM,IAAI;YACnB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,cAAc;YACzB,WAAW,EAAE,+BAA+B;QAC9C;QAEA,MAAM,kBAAkB;YACtB,GAAG,YAAY;YACf,OAAO;mBAAI,aAAa,KAAK;gBAAE;aAAQ;QACzC;QAEA,gBAAgB;QAChB,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,aAAa,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;QACzE,SAAS;IACX;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,OAAO,EAAE,qBAAO,8OAAC,kNAAA,CAAA,aAAU;YAAC,MAAM;YAAI,WAAU;;;;;;QACzD,IAAI,KAAK,UAAU,EAAE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;YAAC,MAAM;YAAI,WAAU;;;;;;QAC1D,IAAI,KAAK,OAAO,EAAE,qBAAO,8OAAC,oMAAA,CAAA,QAAS;YAAC,MAAM;YAAI,WAAU;;;;;;QACxD,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;YAAC,MAAM;YAAI,WAAU;;;;;;IACvC;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,OAAO,2BACL,8OAAC,kNAAA,CAAA,aAAU;YAAC,MAAM;YAAI,WAAU;;;;;iCAChC,8OAAC,sMAAA,CAAA,SAAM;YAAC,MAAM;YAAI,WAAU;;;;;;IAChC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,aAAa;YAC/B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,WAAW,KAAK,CAAC;QACnC,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;QACvC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,KAAK,QAAQ,CAAC,OAAO,IAAI;QAC/B,IAAI,KAAK,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QACpD,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;IAC9B;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB,gBAAgB,GAAG,cAAc,IAAI,CAAC,MAAM,EAAE,cAAc,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG;QAC1H,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,IAAI,eAAe,cAAc,KAAK;QACtC,QAAQ,GAAG,CAAC,4BAA4B,cAAc,UAAU,GAAG;QAEnE,2DAA2D;QAC3D,KAAK,MAAM,YAAY,YAAa;YAClC,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,MAAM,SAAS,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,EAAE,IAAI,KAAK;YACxE,IAAI,UAAU,WAAW,UAAU,OAAO,KAAK,EAAE;gBAC/C,eAAe,OAAO,KAAK;gBAC3B,QAAQ,GAAG,CAAC,6BAA6B,aAAa,MAAM,EAAE;YAChE,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF;QAEA,QAAQ,GAAG,CAAC,0BAA0B,cAAc,UAAU,GAAG;QACjE,OAAO,gBAAgB,EAAE;IAC3B;IAEA,MAAM,gBAAgB,kBAAkB,MAAM,CAAC,CAAA,OAC7C,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG1D,MAAM,kBAAkB,CAAC,OAAoC,QAAgB,CAAC;QAC5E,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;YACtB,MAAM,SAAS,KAAK,IAAI,KAAK;YAC7B,MAAM,WAAW,KAAK,IAAI,KAAK;YAC/B,MAAM,OAAO;YACb,MAAM,aAAa,cAAc,SAAS,KAAK,IAAI;YACnD,MAAM,aAAa,gBAAgB,GAAG,CAAC,KAAK,IAAI;YAChD,MAAM,SAAS,QAAQ;YAEvB,iCAAiC;YACjC,MAAM,sBAAsB,UAAU,KAAK,UAAU,IAAI,cAAc,iBAAiB,SAAS,KAAK,IAAI;YAC1G,MAAM,iBAAiB,UAAU,KAAK,OAAO,IAAI,cAAc,eAAe,SAAS,KAAK,IAAI;YAEhG,qBACE,8OAAC;;kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAK;wBAClC,WAAW,CAAC,oDAAoD,EAC9D,aACI,sCACA,uBAAuB,iBACvB,2DACA,gDACJ;wBACF,OAAO;4BAAE,YAAY,GAAG,OAAO,EAAE,CAAC;wBAAC;wBACnC,SAAS;4BACP,IAAI,QAAQ;gCACV,gBAAgB;4BAClB,OAAO,IAAI,UAAU;gCACnB,aAAa,KAAK,IAAI;4BACxB;wBACF;kCAEA,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,UAAU,YAAY;gCACtB,YAAY,cAAc,KAAK,IAAI,EAAE;8CACtC,8OAAC;oCAAI,WAAU;8CACZ,CAAC,kCACA;;0DACE,8OAAC;gDAAE,WAAU;0DACV,KAAK,IAAI,CAAC,OAAO,CAAC,oCAAoC;;;;;;4CAExD,wBACC,8OAAC;gDAAE,WAAU;;oDACV,KAAK,QAAQ;oDAAC;oDAAI,eAAe,KAAK,IAAI;;;;;;;4CAG9C,YAAY,WAAW,QAAQ,KAAK,KAAK,kBACxC,8OAAC;gDAAE,WAAU;;oDACV,KAAK,KAAK,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;gCAM5B,YAAY,CAAC,kCACZ,8OAAC,oNAAA,CAAA,cAAW;oCACV,MAAM;oCACN,WAAW,CAAC,mCAAmC,EAC7C,aAAa,eAAe,IAC5B;;;;;;;;;;;;;;;;;oBAMT,YAAY,cAAc,WAAW,QAAQ,KAAK,KAAK,kBACtD,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,KAAK,KAAK,EAAE,QAAQ;;;;;;;eAxDjC,KAAK,IAAI;;;;;QA6DvB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;YACjB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC9B,8OAAC;sCAAE;;;;;;;;;;;;;;;;;QAIX;QAEA,IAAI,aAAa,OAAO,EAAE;YACxB,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC,aAAa,IAAI;;;;;;0CACnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,MAAM,OAAO,SAAS,aAAa,CAAC;4CACpC,KAAK,IAAI,GAAG,CAAC,qBAAqB,EAAE,mBAAmB,aAAa,IAAI,GAAG;4CAC3E,KAAK,QAAQ,GAAG,aAAa,IAAI;4CACjC,SAAS,IAAI,CAAC,WAAW,CAAC;4CAC1B,KAAK,KAAK;4CACV,SAAS,IAAI,CAAC,WAAW,CAAC;wCAC5B;wCACA,SAAQ;wCACR,MAAK;;0DAEL,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;oCAGxC,CAAC,iBAAiB,CAAC,6BAClB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,aAAa,aAAa,IAAI;wCAC7C,SAAQ;kDACT;;;;;;oCAIF,+BACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,aAAa,aAAa,IAAI;wCAC7C,SAAQ;kDACT;;;;;;oCAIF,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,8OAAC,gJAAA,CAAA,UAAiB;wBAChB,KAAK,CAAC,qBAAqB,EAAE,mBAAmB,aAAa,IAAI,GAAG;wBACpE,UAAU,iBAAiB;wBAC3B,cAAc,CAAC;4BACb,uBAAuB;4BACvB,IAAI,cAAc;gCAChB,MAAM,kBAAkB;oCACtB,GAAG,YAAY;oCACf,gBAAgB,aAAa,cAAc,GAAG;oCAC9C,cAAc,IAAI,OAAO,WAAW;gCACtC;gCACA,gBAAgB;gCAChB,iBAAiB;4BACnB;wBACF;;;;;;;;;;;;QAIR;QAEA,IAAI,aAAa,UAAU,EAAE;YAC3B,qBACE,8OAAC,gJAAA,CAAA,UAAc;gBACb,MAAM;gBACN,cAAc;gBACd,kBAAkB,CAAC;oBACjB,gBAAgB;oBAChB,iBAAiB;gBACnB;;;;;;QAGN;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyC,aAAa,IAAI;;;;;;8BACxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAE;gCAAY,aAAa,QAAQ;;;;;;;sCACpC,8OAAC;;gCAAE;gCAAO,eAAe,aAAa,IAAI;;;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO,mBAAmB,KAAK;gBAAI;gBAC9C,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAC9B,CAAC,oBAAoB;;;;;;;kDAExB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,iCAAmB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,MAAM;;;;;iEAAS,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;;;;;;;;;;;;4BAIvE,CAAC,kCACA;;kDACE,8OAAC;wCAAG,WAAU;kDAAyC,cAAc,IAAI;;;;;;kDACzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;kCAOpB,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,MAAM,GAAG,mBACpB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;oCAC/B,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,GAAG;;;;;;;4BAI3E,gBAAgB;4BAEhB,cAAc,MAAM,KAAK,mBACxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;;4CAAE;4CAAwB,YAAY,MAAM,GAAG,IAAI,WAAW;;;;;;;oCAC9D,6BACC,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;oBAMnC,CAAC,oBAAoB,8BACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,AAAC,aAAa,aAAa,CAAC,MAAM,GAAG,CAAC,iBAAiB,eAAe,CAAC,IAAK;gDAAK;;;;;;;;;;;;;8CAGjG,8OAAC,oIAAA,CAAA,WAAQ;oCACP,OAAO,AAAC,aAAa,aAAa,CAAC,MAAM,GAAG,CAAC,iBAAiB,eAAe,CAAC,IAAK;oCACnF,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIH,8OAAC,yLAAA,CAAA,kBAAe;kCACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;4BAAE;4BACrB,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,MAAM;gCAAE,QAAQ;4BAAE;4BAClB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,kNAAA,CAAA,aAAU;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAG1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,MAAM,IAAI;wDACrB,WAAU;kEACX;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa;wDAC5B,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAKL,8OAAC;wCACC,KAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAQnB,CAAC,2BACA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}]}