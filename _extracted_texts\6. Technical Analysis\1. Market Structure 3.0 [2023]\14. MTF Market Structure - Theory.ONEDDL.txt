Extracted from: 14. MTF Market Structure - Theory.ONEDDL.pdf
Original file: 6. Technical Analysis/1. Market Structure 3.0 [2023]/14. MTF Market Structure - Theory.ONEDDL.pdf
Extraction date: 2025-07-06T09:52:17.613Z
File type: PDF
================================================================================

MTF Market Structure - Theory
Market Structure 3.0
MTF Analysis ->  analysing multiple timeframes together
Price is fractal.
   A run on a higher timeframe, is a trend on a lower timeframe.
LTF price action forms -> HTF price action, which forecasts -> HTF runs, which are made up of -> 
LTF price action. 
What happens on the HTF, must first happen on the LTF. 
 
https://s3.tradingview.com/snapshots/k/K8Z6MNBe.png 
 
Consistent Actions = Consistent Results
Utilising a set number of timeframes for market structure is key to building consistency
HTF -> Narrative: are we trading a continuation or pullback? 

MTF -> Immediate bias: confirms when HTF continuation is ending & pullback is starting (vice 
versa) 
LTF -> Execution: confirms MTF turning points
 
Examples:
HTF -> 4H / MTF -> M15 / LTF -> M1
HTF-> D / MTF -> 4H / LTF -> M15
 
Know the HTF narrative, actively monitor the LTF price action, and allow the trade setup to 
present itself. 
 
https://s3.tradingview.com/snapshots/m/MKSzxONf.png 
 
Our mission is to observe how price develops around points of interest (POIs) and listen to what 
the market communicates to us with a neutral mindset. 

LTF structural development within HTF POIs helps guide us to be in the flow of the market and 
execute in harmony with the market. 
It's all a mechanical framework to help guide us through the order flow of the market.