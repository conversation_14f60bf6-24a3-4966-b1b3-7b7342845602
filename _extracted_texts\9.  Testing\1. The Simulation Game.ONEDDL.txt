Extracted from: 1. The Simulation Game.ONEDDL.docx
Original file: 9.  Testing/1. The Simulation Game.ONEDDL.docx
Extraction date: 2025-07-06T09:52:18.171Z
File type: DOCX
================================================================================

The Simulation Game

8. 🧪 TESTING

Congratulations, you have now graduated from the Building Phase!

Please take a moment to re-fill out your Trader Scorecard and reflect on how far you have come since the start of the course. 

You are now ready to commence the Testing Phase...



 

There are three levels of knowledge:

IDEA -> information is presented to you by someone else, you evaluate it against your own opinions, but you are not quite sure what to make of it yet. 

BELIEF -> you become convinced that what you have been told is true. 

KNOWING -> this is the knowledge you carry within yourself. It's what you know to be true because you've experienced it first hand. 

 

Knowing is the most powerful form of awareness. 

You can only acquire this with your trading through practice and personal experience.

The quickest and most effective way to achieve this is through extensive testing of your strategy. 

 

The only way you will have extreme confidence in your trading plan is when you have put the work in to prove its edge to yourself over a large sample size of trades and various market conditions. 

This is how you will be able to flawlessly execute with minimal emotional interference during inevitable periods of drawdown.  

If you want to become an outlier, then repetition is your friend. 



 

Make your work feel like play. 

GOLDILOCKS RULE -> humans experience peak motivation when working on tasks that are right on the edge of their current abilities. 

Too easy, you'll get bored and switch off. 

Too hard, you'll feel overwhelmed and anxious. 

Both lead to failure.

The human brain loves a challenge, but only if it is within an optimal zone of difficulty. 



 

Jumping straight into hardcore backtesting without first refining your plan and building a solid understanding of your strategy, will most likely lead to you feeling overwhelmed and confused, ultimately hindering your progression and knocking your confidence. 

 

Instead, follow this 3 stage journey throughout the Testing Phase:

1. CASE STUDIES -> collect data/examples to sense check your plan & prove it has an edge. 

2. FORWARD TEST -> daily/weekly markups watching live price action / hindsight. 

3. BACKTEST -> practice skillset of trade plan execution on historical price action in a simulated market environment. 

 

Start trading a demo account & build confidence executing your plan live with no capital at risk. 

Consider 1-1 coaching sessions for help refining your edge and correcting any remaining errors in your approach.