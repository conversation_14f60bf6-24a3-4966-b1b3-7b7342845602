'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import CustomVideoPlayer from '@/components/video/CustomVideoPlayer';
import DocumentViewer from '@/components/document/DocumentViewer';
import { 
  ArrowLeft, 
  FileText, 
  PlayCircle, 
  Image as ImageIcon, 
  Download,
  Search,
  ChevronRight,
  ChevronDown,
  StickyNote,
  Users,
  BarChart,
  Clock,
  Target,
  Folder,
  FolderOpen
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { CourseStructure, CourseFile, CoursePart, UserProgress, VideoAnalysis } from '@/types/course';
import { useHotkeys } from 'react-hotkeys-hook';

export default function ModulePage() {
  const params = useParams();
  const router = useRouter();
  const [courseStructure, setCourseStructure] = useState<CourseStructure | null>(null);
  const [currentModule, setCurrentModule] = useState<CoursePart | null>(null);
  const [selectedPart, setSelectedPart] = useState<CourseFile | null>(null);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedParts, setExpandedParts] = useState<Set<string>>(new Set());
  const [videoAnalysis, setVideoAnalysis] = useState<VideoAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [notes, setNotes] = useState<string>('');
  const [showNotes, setShowNotes] = useState(false);
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const notesRef = useRef<HTMLTextAreaElement>(null);

  const modulePath = decodeURIComponent(Array.isArray(params.module) ? params.module.join('/') : params.module || '');

  // Keyboard shortcuts
  useHotkeys('ctrl+/', () => setShowNotes(!showNotes));
  useHotkeys('ctrl+b', () => setSidebarCollapsed(!sidebarCollapsed));
  useHotkeys('escape', () => setSelectedPart(null));

  useEffect(() => {
    loadCourseStructure();
    loadUserProgress();
  }, []);

  useEffect(() => {
    if (courseStructure && modulePath) {
      findModule();
    }
  }, [courseStructure, modulePath]);

  useEffect(() => {
    if (selectedPart?.isVideo) {
      loadExistingAnalysis(selectedPart.path);
    } else {
      setVideoAnalysis(null);
    }
  }, [selectedPart]);

  const loadCourseStructure = async () => {
    try {
      const response = await fetch('/api/course-structure');
      const data = await response.json();
      setCourseStructure(data);
    } catch (error) {
      console.error('Failed to load course structure:', error);
    }
  };

  const loadUserProgress = async () => {
    const auth = localStorage.getItem('courseAuth');
    if (auth) {
      const { username } = JSON.parse(auth);
      try {
        const response = await fetch(`/api/user-progress?username=${encodeURIComponent(username)}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setUserProgress(data.progress);
          }
        }
      } catch (error) {
        console.error('Failed to load user progress from server:', error);
        // Fallback to localStorage
        const savedProgress = localStorage.getItem(`progress_${username}`);
        if (savedProgress) {
          setUserProgress(JSON.parse(savedProgress));
        }
      }
    }
  };

  const saveUserProgress = async (updatedProgress: UserProgress) => {
    try {
      const response = await fetch('/api/user-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: updatedProgress.username,
          progress: updatedProgress
        })
      });
      
      if (response.ok) {
        // Also save to localStorage as backup
        localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));
      } else {
        console.error('Failed to save progress to server');
        // Fallback to localStorage only
        localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));
      }
    } catch (error) {
      console.error('Error saving progress:', error);
      // Fallback to localStorage only
      localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));
    }
  };

  const findModule = () => {
    if (!courseStructure) return;

    console.log('🔍 Finding module for path:', modulePath);
    console.log('📚 Available modules:', courseStructure.modules.map(m => ({ name: m.name, path: m.path, partsCount: m.parts?.length || 0 })));

    const findModuleRecursive = (modules: CoursePart[]): CoursePart | null => {
      for (const module of modules) {
        console.log('🔎 Checking module:', module.name, 'path:', module.path, 'vs modulePath:', modulePath);
        if (module.path === modulePath) {
          console.log('✅ Found matching module:', module.name, 'with', module.parts?.length || 0, 'parts');
          return module;
        }
        if (module.type === 'module' && module.parts) {
          const found = findModuleRecursive(module.parts as CoursePart[]);
          if (found) return found;
        }
      }
      return null;
    };

    const found = findModuleRecursive(courseStructure.modules);
    console.log('🎯 Final result:', found ? `Found ${found.name} with ${found.parts?.length || 0} parts` : 'No module found');
    setCurrentModule(found);
    
    // Auto-select first available part if no part is selected
    if (found && !selectedPart) {
      const firstPart = findFirstPart(found.parts);
      if (firstPart) {
        setSelectedPart(firstPart);
      }
    }
  };

  const findFirstPart = (parts: (CourseFile | CoursePart)[]): CourseFile | null => {
    // First, look for documents
    for (const part of parts) {
      if (part.type === 'file' && (part as CourseFile).isDocument) {
        return part as CourseFile;
      }
    }

    // Then look for videos if no documents found
    for (const part of parts) {
      if (part.type === 'file' && (part as CourseFile).isVideo) {
        return part as CourseFile;
      }
    }

    // Finally, look for any other files
    for (const part of parts) {
      if (part.type === 'file') {
        return part as CourseFile;
      }
    }

    // Recursively search in folders/modules
    for (const part of parts) {
      if (part.type === 'folder' || part.type === 'module') {
        const found = findFirstPart((part as CoursePart).parts);
        if (found) return found;
      }
    }

    return null;
  };

  const togglePartExpansion = (partName: string) => {
    const newExpanded = new Set(expandedParts);
    if (newExpanded.has(partName)) {
      newExpanded.delete(partName);
    } else {
      newExpanded.add(partName);
    }
    setExpandedParts(newExpanded);
  };

  const loadExistingAnalysis = async (videoPath: string) => {
    // Clear previous analysis first
    setVideoAnalysis(null);
    setIsAnalyzing(false);
    
    try {
      const response = await fetch('/api/analyze-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ videoPath, checkOnly: true })
      });
      if (response.ok) {
        const analysis = await response.json();
        setVideoAnalysis(analysis);
      } else {
        // No existing analysis found, keep videoAnalysis as null
        console.log('No existing analysis found for this video');
      }
    } catch (error) {
      console.log('No existing analysis found for this video');
      // Keep videoAnalysis as null if there's an error
    }
  };

  const analyzeVideo = async (videoPath: string) => {
    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/analyze-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ videoPath })
      });
      const analysis = await response.json();
      setVideoAnalysis(analysis);
    } catch (error) {
      console.error('Failed to analyze video:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const saveNote = () => {
    if (!userProgress || !notes.trim()) return;

    const newNote = {
      id: Date.now().toString(),
      content: notes.trim(),
      timestamp: new Date().toISOString(),
      videoPath: selectedPart?.path,
      videoTime: 0 // TODO: Get current video time
    };

    const updatedProgress = {
      ...userProgress,
      notes: [...userProgress.notes, newNote]
    };

    setUserProgress(updatedProgress);
    localStorage.setItem(`progress_${userProgress.username}`, JSON.stringify(updatedProgress));
    setNotes('');
  };

  const getFileIcon = (file: CourseFile) => {
    if (file.isVideo) return <PlayCircle size={16} className="text-red-400" />;
    if (file.isDocument) return <FileText size={16} className="text-blue-400" />;
    if (file.isImage) return <ImageIcon size={16} className="text-green-400" />;
    return <FileText size={16} className="text-gray-400" />;
  };

  const getFolderIcon = (path: string, isExpanded: boolean) => {
    return isExpanded ? 
      <FolderOpen size={16} className="text-yellow-400" /> : 
      <Folder size={16} className="text-yellow-400" />;
  };

  const toggleFolder = (folderPath: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath);
    } else {
      newExpanded.add(folderPath);
    }
    setExpandedFolders(newExpanded);
  };

  const navigateToFolder = (folderPath: string) => {
    const pathParts = folderPath.split('/');
    setCurrentPath(pathParts);
  };

  const navigateBack = () => {
    if (currentPath.length > 0) {
      setCurrentPath(currentPath.slice(0, -1));
    }
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    if (mb > 1000) return `${(mb / 1024).toFixed(1)} GB`;
    return `${mb.toFixed(1)} MB`;
  };

  const getCurrentParts = () => {
    console.log('📂 getCurrentParts called');
    console.log('📂 currentModule:', currentModule ? `${currentModule.name} with ${currentModule.parts?.length || 0} parts` : 'null');
    console.log('📂 currentPath:', currentPath);

    if (!currentModule) {
      console.log('❌ No currentModule, returning empty array');
      return [];
    }

    let currentParts = currentModule.parts;
    console.log('📂 Initial currentParts:', currentParts?.length || 0, 'items');

    // Navigate through the path to get current folder contents
    for (const pathPart of currentPath) {
      console.log('📂 Navigating to path part:', pathPart);
      const folder = currentParts.find(p => p.name === pathPart && p.type === 'folder');
      if (folder && 'parts' in folder && folder.parts) {
        currentParts = folder.parts;
        console.log('📂 Found folder, now have', currentParts.length, 'parts');
      } else {
        console.log('❌ Folder not found, returning empty array');
        return [];
      }
    }

    console.log('📂 Final currentParts:', currentParts?.length || 0, 'items');
    return currentParts || [];
  };

  const filteredParts = getCurrentParts().filter(part => 
    part.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderPartsList = (parts: (CourseFile | CoursePart)[], level: number = 0) => {
    return parts.map((part, index) => {
      const isFile = part.type === 'file';
      const isFolder = part.type === 'folder';
      const file = part as CourseFile;
      const isSelected = selectedPart?.path === part.path;
      const isExpanded = expandedFolders.has(part.path);
      const indent = level * 20;

      // Check if document is completed
      const isDocumentCompleted = isFile && file.isDocument && userProgress?.viewedDocuments?.includes(file.path);
      const isVideoWatched = isFile && file.isVideo && userProgress?.watchedVideos?.includes(file.path);
      
      return (
        <div key={part.path}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            className={`p-3 rounded-lg border cursor-pointer transition-all ${
              isSelected
                ? 'bg-blue-500/20 border-blue-400/50'
                : isDocumentCompleted || isVideoWatched
                ? 'bg-gray-500/20 border-gray-400/30 hover:bg-gray-500/30'
                : 'bg-white/5 border-white/10 hover:bg-white/10'
            }`}
            style={{ marginLeft: `${indent}px` }}
            onClick={() => {
              if (isFile) {
                setSelectedPart(file);
              } else if (isFolder) {
                toggleFolder(part.path);
              }
            }}
          >
            <div className="flex items-center space-x-3">
              {isFile && getFileIcon(file)}
              {isFolder && getFolderIcon(part.path, isExpanded)}
              <div className="flex-1 min-w-0">
                {!sidebarCollapsed && (
                  <>
                    <p className="text-white text-sm font-medium truncate">
                      {part.name.replace(/\.ONEDDL\.(mp4|docx|pdf|ts|png)$/, '')}
                    </p>
                    {isFile && (
                      <p className="text-white/60 text-xs">
                        {file.fileType} • {formatFileSize(file.size)}
                      </p>
                    )}
                    {isFolder && 'parts' in part && part.parts && (
                      <p className="text-white/60 text-xs">
                        {part.parts.length} items
                      </p>
                    )}
                  </>
                )}
              </div>
              {isFolder && !sidebarCollapsed && (
                <ChevronDown 
                  size={14} 
                  className={`text-white/60 transition-transform ${
                    isExpanded ? 'rotate-180' : ''
                  }`} 
                />
              )}
            </div>
          </motion.div>
          
          {isFolder && isExpanded && 'parts' in part && part.parts && (
            <div className="mt-1">
              {renderPartsList(part.parts, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  const renderFileContent = () => {
    if (!selectedPart) {
      return (
        <div className="flex items-center justify-center h-96 text-white/60">
          <div className="text-center">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>Select a file to view its content</p>
          </div>
        </div>
      );
    }

    if (selectedPart.isVideo) {
      return (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">{selectedPart.name}</h2>
            <div className="flex items-center space-x-2">
              <Button 
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = `/api/serve-file?path=${encodeURIComponent(selectedPart.path)}`;
                  link.download = selectedPart.name;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
                variant="outline"
                size="sm"
              >
                <Download size={16} className="mr-2" />
                Download
              </Button>
              {!videoAnalysis && !isAnalyzing && (
                <Button 
                  onClick={() => analyzeVideo(selectedPart.path)}
                  variant="outline"
                >
                  Generate AI Analysis
                </Button>
              )}
              {videoAnalysis && (
                <Button 
                  onClick={() => analyzeVideo(selectedPart.path)}
                  variant="outline"
                >
                  Regenerate Analysis
                </Button>
              )}
              {isAnalyzing && (
                <div className="flex items-center space-x-2 text-white/70">
                  <div className="animate-spin w-4 h-4 border-2 border-white/30 border-t-white rounded-full"></div>
                  <span className="text-sm">Analyzing with AI...</span>
                </div>
              )}
            </div>
          </div>
          
          <CustomVideoPlayer
            src={`/api/serve-file?path=${encodeURIComponent(selectedPart.path)}`}
            analysis={videoAnalysis || undefined}
            onTimeUpdate={(time) => {
              // Update user progress
              if (userProgress) {
                const updatedProgress = {
                  ...userProgress,
                  totalWatchTime: userProgress.totalWatchTime + 1,
                  lastAccessed: new Date().toISOString()
                };
                setUserProgress(updatedProgress);
                saveUserProgress(updatedProgress);
              }
            }}
          />
        </div>
      );
    }

    if (selectedPart.isDocument) {
      return (
        <DocumentViewer
          file={selectedPart}
          userProgress={userProgress}
          onProgressUpdate={(updatedProgress) => {
            setUserProgress(updatedProgress);
            saveUserProgress(updatedProgress);
          }}
        />
      );
    }

    return (
      <div className="glass-morphism p-6 rounded-lg">
        <h2 className="text-xl font-semibold text-white mb-4">{selectedPart.name}</h2>
        <div className="text-white/80">
          <p>File type: {selectedPart.fileType}</p>
          <p>Size: {formatFileSize(selectedPart.size)}</p>
        </div>
      </div>
    );
  };

  if (!currentModule) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass-morphism p-8 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
          <p className="text-white">Loading module...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <motion.div
        animate={{ width: sidebarCollapsed ? 80 : 400 }}
        transition={{ duration: 0.3 }}
        className="bg-black/20 backdrop-blur-md border-r border-white/10 flex flex-col"
      >
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft size={16} className="mr-2" />
              {!sidebarCollapsed && 'Back to Dashboard'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="text-white hover:bg-white/10"
            >
              {sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronDown size={16} />}
            </Button>
          </div>
          
          {!sidebarCollapsed && (
            <>
              <h1 className="text-white font-semibold text-lg mb-2">{currentModule.name}</h1>
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50" />
                <Input
                  placeholder="Search parts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                />
              </div>
            </>
          )}
        </div>

        <div className="flex-1 overflow-y-auto custom-scrollbar p-4 space-y-2">
          {currentPath.length > 0 && (
            <Button
              variant="ghost"
              onClick={navigateBack}
              className="w-full text-left text-white/70 hover:text-white hover:bg-white/10 mb-4"
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to {currentPath.length > 1 ? currentPath[currentPath.length - 2] : 'Module'}
            </Button>
          )}
          
          {renderPartsList(filteredParts)}
          
          {filteredParts.length === 0 && (
            <div className="text-center py-8 text-white/60">
              <FileText size={48} className="mx-auto mb-4 opacity-50" />
              <p>No files found in this {currentPath.length > 0 ? 'folder' : 'module'}</p>
              {searchQuery && (
                <p className="text-sm mt-2">Try adjusting your search term</p>
              )}
            </div>
          )}
        </div>

        {!sidebarCollapsed && userProgress && (
          <div className="p-4 border-t border-white/10">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/70">Progress</span>
                <span className="text-white">
                  {Math.round((userProgress.watchedVideos.length / (courseStructure?.totalVideos || 1)) * 100)}%
                </span>
              </div>
              <Progress 
                value={(userProgress.watchedVideos.length / (courseStructure?.totalVideos || 1)) * 100} 
                className="h-2" 
              />
            </div>
          </div>
        )}
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1 p-6">
          {renderFileContent()}
        </div>

        {/* Notes Panel */}
        <AnimatePresence>
          {showNotes && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: 200 }}
              exit={{ height: 0 }}
              className="border-t border-white/10 bg-black/20 backdrop-blur-md overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-white font-medium flex items-center gap-2">
                    <StickyNote size={16} />
                    Universal Notes
                  </h3>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={saveNote}
                      disabled={!notes.trim()}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      Save Note
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setShowNotes(false)}
                      className="text-white hover:bg-white/10"
                    >
                      ×
                    </Button>
                  </div>
                </div>
                <textarea
                  ref={notesRef}
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Take notes... (Ctrl+/ to toggle)"
                  className="w-full h-24 bg-white/10 border border-white/20 rounded-lg p-3 text-white placeholder:text-white/50 resize-none focus:outline-none focus:border-white/40"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Floating Notes Button */}
        {!showNotes && (
          <Button
            onClick={() => setShowNotes(true)}
            className="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 rounded-full p-3"
            title="Open Notes (Ctrl+/)"
          >
            <StickyNote size={20} />
          </Button>
        )}
      </div>
    </div>
  );
}