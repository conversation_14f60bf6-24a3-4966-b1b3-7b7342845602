#!/usr/bin/env python3
"""
Document Text Extraction Script for Trading Course
Extracts text from PDF, DOCX, TXT files and saves them as .txt files
"""

import os
import sys
from pathlib import Path
import PyPDF2
import docx
import fitz  # PyMuPDF
from docx import Document

def install_requirements():
    """Install required packages"""
    import subprocess
    packages = [
        'PyPDF2',
        'python-docx', 
        'PyMuPDF'
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"✗ Failed to install {package}")

def extract_pdf_text_pypdf2(file_path):
    """Extract text from PDF using PyPDF2"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page_num, page in enumerate(pdf_reader.pages, 1):
                page_text = page.extract_text()
                if page_text.strip():
                    text += f"--- Page {page_num} ---\n{page_text}\n\n"
            return text.strip()
    except Exception as e:
        print(f"PyPDF2 extraction failed for {file_path}: {e}")
        return None

def extract_pdf_text_pymupdf(file_path):
    """Extract text from PDF using PyMuPDF (more reliable)"""
    try:
        doc = fitz.open(file_path)
        text = ""
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            if page_text.strip():
                text += f"--- Page {page_num + 1} ---\n{page_text}\n\n"
        doc.close()
        return text.strip()
    except Exception as e:
        print(f"PyMuPDF extraction failed for {file_path}: {e}")
        return None

def extract_docx_text(file_path):
    """Extract text from DOCX files"""
    try:
        doc = Document(file_path)
        text = ""
        
        # Extract paragraphs
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text += paragraph.text + "\n"
        
        # Extract tables
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    text += " | ".join(row_text) + "\n"
        
        return text.strip()
    except Exception as e:
        print(f"DOCX extraction failed for {file_path}: {e}")
        return None

def extract_txt_text(file_path):
    """Extract text from TXT files"""
    try:
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    return file.read()
            except UnicodeDecodeError:
                continue
        return None
    except Exception as e:
        print(f"TXT extraction failed for {file_path}: {e}")
        return None

def process_file(file_path, output_dir):
    """Process a single file and extract text"""
    file_path = Path(file_path)
    extension = file_path.suffix.lower()
    
    print(f"Processing: {file_path.name}")
    
    extracted_text = None
    
    if extension == '.pdf':
        # Try PyMuPDF first (more reliable), fallback to PyPDF2
        extracted_text = extract_pdf_text_pymupdf(file_path)
        if not extracted_text:
            extracted_text = extract_pdf_text_pypdf2(file_path)
    
    elif extension == '.docx':
        extracted_text = extract_docx_text(file_path)
    
    elif extension == '.txt':
        extracted_text = extract_txt_text(file_path)
    
    else:
        print(f"  ⚠️  Unsupported file type: {extension}")
        return False
    
    if extracted_text and extracted_text.strip():
        # Create output file path
        relative_path = file_path.relative_to(Path.cwd().parent)
        output_file = output_dir / f"{relative_path.with_suffix('.txt')}"
        
        # Create directory structure
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Write extracted text
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"Extracted from: {file_path.name}\n")
                f.write(f"Original file: {relative_path}\n")
                f.write(f"Extraction date: {__import__('datetime').datetime.now().isoformat()}\n")
                f.write("=" * 80 + "\n\n")
                f.write(extracted_text)
            
            print(f"  ✓ Extracted to: {output_file}")
            return True
        except Exception as e:
            print(f"  ✗ Failed to write output: {e}")
            return False
    else:
        print(f"  ⚠️  No text extracted")
        return False

def scan_and_process_documents():
    """Scan for documents and process them"""
    
    # Get the trading course directory (parent of this script)
    course_dir = Path.cwd().parent
    output_dir = course_dir / "_extracted_texts"
    
    print(f"Course directory: {course_dir}")
    print(f"Output directory: {output_dir}")
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Supported extensions
    supported_extensions = {'.pdf', '.docx', '.txt'}
    
    # Find all supported files
    files_to_process = []
    for ext in supported_extensions:
        files_to_process.extend(course_dir.rglob(f"*{ext}"))
    
    # Filter out already extracted files
    files_to_process = [f for f in files_to_process if not f.name.startswith('_extracted_')]
    
    print(f"\nFound {len(files_to_process)} files to process:")
    for file in files_to_process:
        print(f"  - {file.relative_to(course_dir)}")
    
    if not files_to_process:
        print("No files found to process!")
        return
    
    print(f"\nStarting extraction...")
    print("=" * 80)
    
    success_count = 0
    for file_path in files_to_process:
        if process_file(file_path, output_dir):
            success_count += 1
        print()
    
    print("=" * 80)
    print(f"Extraction complete!")
    print(f"Successfully processed: {success_count}/{len(files_to_process)} files")
    print(f"Extracted files saved to: {output_dir}")

if __name__ == "__main__":
    print("Trading Course Document Text Extractor")
    print("=" * 50)
    
    # Check if required packages are available, install if needed
    try:
        import PyPDF2
        import docx
        import fitz
        print("✓ All required packages are available")
    except ImportError as e:
        print(f"⚠️  Missing packages. Installing...")
        install_requirements()
        print("Please run the script again after installation.")
        sys.exit(1)
    
    scan_and_process_documents()