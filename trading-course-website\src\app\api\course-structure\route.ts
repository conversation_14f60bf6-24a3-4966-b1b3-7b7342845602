import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { PREDEFINED_MODULES } from '@/config/modules';

export async function GET() {
  try {
    const courseStructurePath = path.join(process.cwd(), 'course-structure.json');
    
    // Check if we have a recent scan (less than 1 hour old)
    if (fs.existsSync(courseStructurePath)) {
      const stats = fs.statSync(courseStructurePath);
      const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
      
      if (stats.mtime > hourAgo) {
        const courseStructure = JSON.parse(fs.readFileSync(courseStructurePath, 'utf8'));
        return NextResponse.json(courseStructure);
      }
    }

    // Trigger a fresh scan
    const scanResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/scan-course`);
    
    if (scanResponse.ok) {
      const scannedStructure = await scanResponse.json();
      return NextResponse.json(scannedStructure);
    } else {
      // Fallback to basic structure if scan fails
      const basicStructure = {
        title: "Photon Trading Zero to Funded 4.0 (2025)",
        modules: PREDEFINED_MODULES.map(module => ({
          type: "module",
          name: module.name,
          path: module.folderPattern,
          parts: [],
          id: module.id,
          description: module.description,
          color: module.color
        })),
        totalModules: PREDEFINED_MODULES.length,
        totalFiles: 0,
        totalVideos: 0,
        totalDocuments: 0,
        estimatedDuration: 0
      };
      return NextResponse.json(basicStructure);
    }
  } catch (error) {
    console.error('Error loading course structure:', error);
    return NextResponse.json(
      { error: 'Failed to load course structure' },
      { status: 500 }
    );
  }
}