'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  PlayCircle, 
  FileText, 
  Clock, 
  TrendingUp, 
  Users, 
  Target,
  BookOpen,
  ChevronRight,
  Calendar,
  BarChart3
} from 'lucide-react';
import { motion } from 'framer-motion';
import { CourseStructure, UserProgress } from '@/types/course';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';

interface CourseDashboardProps {
  username: string;
  courseStructure: CourseStructure;
  userProgress: UserProgress;
  onModuleClick: (modulePath: string) => void;
  onLogout: () => void;
}

export default function CourseDashboard({
  username,
  courseStructure,
  userProgress,
  onModuleClick,
  onLogout
}: CourseDashboardProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showAnalytics, setShowAnalytics] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  const completionPercentage = Math.round(
    (userProgress.completedModules.length / courseStructure.totalModules) * 100
  );

  const continueModule = userProgress.currentModule || courseStructure.modules[0]?.path;
  const currentModuleName = courseStructure.modules.find(m => m.path === continueModule)?.name || 'Start Here';

  const statsCards = [
    {
      title: 'Course Progress',
      value: `${completionPercentage}%`,
      icon: Target,
      description: `${userProgress.completedModules.length}/${courseStructure.totalModules} modules completed`,
      color: 'text-green-400'
    },
    {
      title: 'Watch Time',
      value: `${Math.round(userProgress.totalWatchTime / 60)}h`,
      icon: Clock,
      description: `${userProgress.analytics.sessionsCount} sessions`,
      color: 'text-blue-400'
    },
    {
      title: 'Learning Streak',
      value: `${userProgress.analytics.learningStreak}`,
      icon: TrendingUp,
      description: 'consecutive days',
      color: 'text-purple-400'
    },
    {
      title: 'Notes Created',
      value: userProgress.notes.length.toString(),
      icon: FileText,
      description: 'personal notes',
      color: 'text-orange-400'
    }
  ];

  return (
    <div className="min-h-screen p-6 space-y-6">
      <div className="flex justify-between items-start mb-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-2"
        >
          <h1 className="text-4xl font-bold text-white">
            Welcome back, {username}
          </h1>
          <p className="text-white/70">
            {currentTime.toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex items-center space-x-4"
        >
          <Avatar className="border-2 border-white/20">
            <AvatarFallback className="bg-white/10 text-white">
              {username.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <Button 
            variant="ghost" 
            onClick={onLogout}
            className="text-white/70 hover:text-white hover:bg-white/10"
          >
            Logout
          </Button>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statsCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white/70 text-sm font-medium">{stat.title}</p>
                    <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                    <p className="text-white/50 text-xs mt-1">{stat.description}</p>
                  </div>
                  <stat.icon size={24} className={stat.color} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="lg:col-span-2"
        >
          <Card className="border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <BookOpen size={20} />
                Continue Learning
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-white/5 rounded-lg border border-white/10">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="text-white font-medium">{currentModuleName}</h3>
                    <p className="text-white/60 text-sm">Pick up where you left off</p>
                  </div>
                  <Button 
                    onClick={() => onModuleClick(continueModule)}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  >
                    Continue
                    <ChevronRight size={16} className="ml-1" />
                  </Button>
                </div>
                <Progress value={completionPercentage} className="h-2" />
              </div>

              <div className="space-y-3">
                <h4 className="text-white font-medium">Course Modules</h4>
                <div className="space-y-2 max-h-80 overflow-y-auto custom-scrollbar">
                  {courseStructure.modules.map((module, index) => {
                    const isCompleted = userProgress.completedModules.includes(module.path);
                    const isCurrent = module.path === userProgress.currentModule;
                    
                    return (
                      <motion.div
                        key={module.path}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`p-3 rounded-lg border cursor-pointer transition-all ${
                          isCurrent 
                            ? 'bg-blue-500/20 border-blue-400/50' 
                            : isCompleted 
                              ? 'bg-green-500/10 border-green-400/30 hover:bg-green-500/20' 
                              : 'bg-white/5 border-white/10 hover:bg-white/10'
                        }`}
                        onClick={() => onModuleClick(module.path)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                              isCompleted 
                                ? 'bg-green-500 text-white' 
                                : isCurrent 
                                  ? 'bg-blue-500 text-white' 
                                  : 'bg-white/20 text-white/70'
                            }`}>
                              {index + 1}
                            </div>
                            <div>
                              <p className="text-white text-sm font-medium">{module.name}</p>
                              <p className="text-white/60 text-xs">
                                {module.parts.filter(p => p.type === 'file').length} parts
                              </p>
                            </div>
                          </div>
                          {isCompleted && (
                            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-400/30">
                              Complete
                            </Badge>
                          )}
                          {isCurrent && !isCompleted && (
                            <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-400/30">
                              Current
                            </Badge>
                          )}
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-6"
        >
          <Card className="border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <BarChart3 size={20} />
                Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm">Videos Watched</span>
                  <span className="text-white font-medium">
                    {userProgress.watchedVideos.length}/{courseStructure.totalVideos}
                  </span>
                </div>
                <Progress 
                  value={(userProgress.watchedVideos.length / courseStructure.totalVideos) * 100} 
                  className="h-2" 
                />
              </div>
              
              <div className="pt-4 border-t border-white/10">
                <p className="text-white/70 text-sm mb-2">Recent Activity</p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Calendar size={14} className="text-blue-400" />
                    <span className="text-white/80 text-xs">
                      Last accessed: {new Date(userProgress.lastAccessed).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <PlayCircle size={14} className="text-green-400" />
                    <span className="text-white/80 text-xs">
                      {userProgress.analytics.averageSessionTime} min avg session
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Users size={20} />
                Learning Community
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/70 text-sm mb-3">
                See how you compare to other learners
              </p>
              <Button 
                variant="outline" 
                onClick={() => setShowAnalytics(true)}
                className="w-full"
              >
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {showAnalytics && (
        <AnalyticsDashboard
          userProgress={userProgress}
          onClose={() => setShowAnalytics(false)}
        />
      )}
    </div>
  );
}