'use client';

import { useEffect, useRef, useState } from 'react';
import { VideoAnalysis } from '@/types/course';
import ConceptMap from '@/components/visualization/ConceptMap';
import VideoAnalysisDialog from './VideoAnalysisDialog';

interface CustomVideoPlayerProps {
  src: string;
  analysis?: VideoAnalysis;
  onTimeUpdate?: (currentTime: number) => void;
  onProgress?: (progress: number) => void;
  initialTime?: number;
}

export default function CustomVideoPlayer({
  src,
  analysis,
  onTimeUpdate,
  onProgress,
  initialTime = 0
}: CustomVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogTab, setDialogTab] = useState<'overview' | 'concept_map' | 'key_points' | 'summary'>('overview');

  useEffect(() => {
    const video = videoRef.current;
    if (!video) {
      return;
    }

    const handleTimeUpdate = () => {
      onTimeUpdate?.(video.currentTime);
    };

    const handleProgress = () => {
      if (video.duration > 0) {
        const progress = (video.currentTime / video.duration) * 100;
        onProgress?.(progress);
      }
    };

    const handleLoadedMetadata = () => {
      if (initialTime > 0) {
        video.currentTime = initialTime;
      }
    };

    const handleLoadStart = () => {
      // Video load started
    };

    const handleCanPlay = () => {
      // Video can play
    };

    const handleCanPlayThrough = () => {
      // Video can play through
    };

    const handleError = (e: Event) => {
      console.error('Video playback error:', video.error?.message || 'Unknown error');
    };

    const handleWaiting = () => {
      // Video waiting for data
    };

    const handleStalled = () => {
      // Video stalled
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('progress', handleProgress);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('canplaythrough', handleCanPlayThrough);
    video.addEventListener('error', handleError);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('stalled', handleStalled);

    return () => {
      console.log('🧹 Cleaning up video event listeners');
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('progress', handleProgress);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('canplaythrough', handleCanPlayThrough);
      video.removeEventListener('error', handleError);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('stalled', handleStalled);
    };
  }, [onTimeUpdate, onProgress, initialTime]);

  const jumpToTimestamp = (timestamp: string) => {
    const video = videoRef.current;
    if (!video) return;

    const timeMatch = timestamp.match(/(\d+):(\d+)/);
    if (timeMatch) {
      const minutes = parseInt(timeMatch[1]);
      const seconds = parseInt(timeMatch[2]);
      const time = minutes * 60 + seconds;
      video.currentTime = time;
    }
  };

  console.log('🎨 About to render video element with src:', src);

  return (
    <div className="space-y-4">
      <div className="relative video-player-container">
        <video
          ref={videoRef}
          className="w-full h-auto bg-black rounded-lg"
          controls
          preload="metadata"
          style={{ maxHeight: '60vh' }}
        >
          <source src={src} type="video/mp4" />
          <p className="text-white p-4">
            Your browser does not support the video tag. 
            <a href={src} download className="text-blue-400 hover:text-blue-300 ml-2">
              Download video instead
            </a>
          </p>
        </video>
      </div>

      {analysis && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="space-y-4">
            <div
              className="glass-morphism-dark p-4 border border-white/20 rounded-lg cursor-pointer hover:border-white/40 transition-all"
              onClick={() => {
                setDialogTab('overview');
                setDialogOpen(true);
              }}
            >
              <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                Video Overview
                <span className="text-xs text-white/60">Click to expand</span>
              </h3>
              <p className="text-white/80 text-sm line-clamp-3">{analysis.overview}</p>
            </div>

            <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
              <h3 className="text-white font-medium mb-2">Quick Note</h3>
              <p className="text-white/80 text-sm">{analysis.quick_note}</p>
            </div>

            {analysis.slides && analysis.slides.length > 0 && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Key Slides</h3>
                <div className="space-y-3 max-h-60 overflow-y-auto custom-scrollbar">
                  {analysis.slides.map((slide, index) => (
                    <div key={index} className="border-l-2 border-blue-400 pl-3 py-2">
                      <h4 className="text-white text-sm font-medium">{slide.title}</h4>
                      <p className="text-white/70 text-xs mt-1">{slide.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            {analysis.concept_map && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3
                  className="text-white font-medium mb-3 flex items-center justify-between cursor-pointer hover:text-white/80"
                  onClick={() => {
                    setDialogTab('concept_map');
                    setDialogOpen(true);
                  }}
                >
                  Concept Map
                  <span className="text-xs text-white/60">Click to expand</span>
                </h3>
                <ConceptMap
                  conceptMap={analysis.concept_map}
                  onOpenDialog={() => {
                    setDialogTab('concept_map');
                    setDialogOpen(true);
                  }}
                />
              </div>
            )}

            {analysis.glossary && analysis.glossary.length > 0 && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Glossary</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto custom-scrollbar">
                  {analysis.glossary.map((item, index) => (
                    <div key={index} className="p-3 bg-white/5 rounded-lg">
                      <h4 className="text-white font-medium text-sm">{item.term}</h4>
                      <p className="text-white/70 text-xs mt-1">{item.definition}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            {analysis.cheat_sheet && analysis.cheat_sheet.length > 0 && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Quick Reference</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto custom-scrollbar">
                  {analysis.cheat_sheet.map((item, index) => (
                    <div key={index} className="text-white/80 text-xs p-2 bg-white/5 rounded">
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {analysis.transcript && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Transcript Timeline</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto custom-scrollbar">
                  {analysis.transcript.split('[').filter(Boolean).slice(0, 10).map((section, index) => {
                    const timestampMatch = section.match(/^(\d+:\d+)\]/);
                    if (timestampMatch) {
                      const timestamp = timestampMatch[1];
                      const text = section.replace(/^\d+:\d+\]/, '').trim();
                      
                      return (
                        <div 
                          key={index} 
                          className="flex items-start space-x-2 cursor-pointer hover:bg-white/5 p-2 rounded"
                          onClick={() => jumpToTimestamp(timestamp)}
                        >
                          <span className="text-blue-400 text-xs font-mono min-w-[40px]">
                            {timestamp}
                          </span>
                          <span className="text-white/80 text-xs">
                            {text.substring(0, 100)}...
                          </span>
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}