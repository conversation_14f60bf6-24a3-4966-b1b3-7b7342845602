'use client';

import { useEffect, useRef } from 'react';
import { VideoAnalysis } from '@/types/course';
import ConceptMap from '@/components/visualization/ConceptMap';

interface CustomVideoPlayerProps {
  src: string;
  analysis?: VideoAnalysis;
  onTimeUpdate?: (currentTime: number) => void;
  onProgress?: (progress: number) => void;
  initialTime?: number;
}

export default function CustomVideoPlayer({
  src,
  analysis,
  onTimeUpdate,
  onProgress,
  initialTime = 0
}: CustomVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  
  console.log('🎬 ===== VIDEO PLAYER COMPONENT RENDER =====');
  console.log('📥 Video src prop:', JSON.stringify(src));
  console.log('🕐 Render timestamp:', new Date().toISOString());

  useEffect(() => {
    console.log('📺 Video useEffect triggered');
    const video = videoRef.current;
    if (!video) {
      console.log('❌ No video ref found');
      return;
    }
    
    console.log('✅ Video element found, adding event listeners');
    console.log('📋 Video element src:', video.src);
    console.log('📋 Video element currentSrc:', video.currentSrc);

    const handleTimeUpdate = () => {
      onTimeUpdate?.(video.currentTime);
    };

    const handleProgress = () => {
      if (video.duration > 0) {
        const progress = (video.currentTime / video.duration) * 100;
        onProgress?.(progress);
      }
    };

    const handleLoadedMetadata = () => {
      console.log('📺 Video metadata loaded');
      console.log('⏱️ Duration:', video.duration);
      if (initialTime > 0) {
        video.currentTime = initialTime;
      }
    };

    const handleLoadStart = () => {
      console.log('🔄 Video load started');
      console.log('📋 Current src:', video.currentSrc);
    };

    const handleCanPlay = () => {
      console.log('✅ Video can play');
    };

    const handleCanPlayThrough = () => {
      console.log('✅ Video can play through');
    };

    const handleError = (e: Event) => {
      console.log('❌ Video error occurred:');
      console.log('📋 Error event:', e);
      console.log('📋 Video error code:', video.error?.code);
      console.log('📋 Video error message:', video.error?.message);
      console.log('📋 Video src:', video.src);
      console.log('📋 Video currentSrc:', video.currentSrc);
    };

    const handleWaiting = () => {
      console.log('⏳ Video waiting for data');
    };

    const handleStalled = () => {
      console.log('🚫 Video stalled');
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('progress', handleProgress);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('canplaythrough', handleCanPlayThrough);
    video.addEventListener('error', handleError);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('stalled', handleStalled);

    return () => {
      console.log('🧹 Cleaning up video event listeners');
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('progress', handleProgress);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('canplaythrough', handleCanPlayThrough);
      video.removeEventListener('error', handleError);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('stalled', handleStalled);
    };
  }, [onTimeUpdate, onProgress, initialTime]);

  const jumpToTimestamp = (timestamp: string) => {
    const video = videoRef.current;
    if (!video) return;

    const timeMatch = timestamp.match(/(\d+):(\d+)/);
    if (timeMatch) {
      const minutes = parseInt(timeMatch[1]);
      const seconds = parseInt(timeMatch[2]);
      const time = minutes * 60 + seconds;
      video.currentTime = time;
    }
  };

  console.log('🎨 About to render video element with src:', src);

  return (
    <div className="space-y-4">
      {/* Debug Panel */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 mb-4">
        <h3 className="text-white font-bold mb-2">🐛 Debug Information</h3>
        <div className="text-sm text-gray-300 space-y-1">
          <div><strong>Video Source:</strong> {src}</div>
          <div><strong>Initial Time:</strong> {initialTime}</div>
          <div><strong>Analysis Available:</strong> {analysis ? 'Yes' : 'No'}</div>
          <div><strong>Port Check:</strong> <a href={src} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300">Test Direct Link</a></div>
        </div>
      </div>

      <div className="relative video-player-container">
        <video
          ref={videoRef}
          className="w-full h-auto bg-black rounded-lg"
          controls
          preload="metadata"
          style={{ maxHeight: '60vh' }}
          onLoadStart={() => console.log('🎬 Video onLoadStart triggered')}
          onError={(e) => console.log('🎬 Video onError triggered:', e)}
          onCanPlay={() => console.log('🎬 Video onCanPlay triggered')}
        >
          <source src={src} type="video/mp4" />
          <p className="text-white p-4">
            Your browser does not support the video tag. 
            <a href={src} download className="text-blue-400 hover:text-blue-300 ml-2">
              Download video instead
            </a>
          </p>
        </video>
      </div>

      {analysis && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="space-y-4">
            <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
              <h3 className="text-white font-medium mb-2">Video Overview</h3>
              <p className="text-white/80 text-sm">{analysis.overview}</p>
            </div>

            <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
              <h3 className="text-white font-medium mb-2">Quick Note</h3>
              <p className="text-white/80 text-sm">{analysis.quick_note}</p>
            </div>

            {analysis.slides && analysis.slides.length > 0 && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Key Slides</h3>
                <div className="space-y-3 max-h-60 overflow-y-auto custom-scrollbar">
                  {analysis.slides.map((slide, index) => (
                    <div key={index} className="border-l-2 border-blue-400 pl-3 py-2">
                      <h4 className="text-white text-sm font-medium">{slide.title}</h4>
                      <p className="text-white/70 text-xs mt-1">{slide.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            {analysis.concept_map && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Concept Map</h3>
                <ConceptMap conceptMap={analysis.concept_map} />
              </div>
            )}

            {analysis.glossary && analysis.glossary.length > 0 && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Glossary</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto custom-scrollbar">
                  {analysis.glossary.map((item, index) => (
                    <div key={index} className="p-3 bg-white/5 rounded-lg">
                      <h4 className="text-white font-medium text-sm">{item.term}</h4>
                      <p className="text-white/70 text-xs mt-1">{item.definition}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            {analysis.cheat_sheet && analysis.cheat_sheet.length > 0 && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Quick Reference</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto custom-scrollbar">
                  {analysis.cheat_sheet.map((item, index) => (
                    <div key={index} className="text-white/80 text-xs p-2 bg-white/5 rounded">
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {analysis.transcript && (
              <div className="glass-morphism-dark p-4 border border-white/20 rounded-lg">
                <h3 className="text-white font-medium mb-3">Transcript Timeline</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto custom-scrollbar">
                  {analysis.transcript.split('[').filter(Boolean).slice(0, 10).map((section, index) => {
                    const timestampMatch = section.match(/^(\d+:\d+)\]/);
                    if (timestampMatch) {
                      const timestamp = timestampMatch[1];
                      const text = section.replace(/^\d+:\d+\]/, '').trim();
                      
                      return (
                        <div 
                          key={index} 
                          className="flex items-start space-x-2 cursor-pointer hover:bg-white/5 p-2 rounded"
                          onClick={() => jumpToTimestamp(timestamp)}
                        >
                          <span className="text-blue-400 text-xs font-mono min-w-[40px]">
                            {timestamp}
                          </span>
                          <span className="text-white/80 text-xs">
                            {text.substring(0, 100)}...
                          </span>
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}