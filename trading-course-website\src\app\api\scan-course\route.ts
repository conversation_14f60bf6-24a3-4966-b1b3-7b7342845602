import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { PREDEFINED_MODULES } from '@/config/modules';

interface CourseFile {
  name: string;
  path: string;
  type: 'file' | 'folder';
  size: number;
  extension: string;
  isVideo: boolean;
  isDocument: boolean;
  isImage: boolean;
  fileType: string;
}

interface CoursePart {
  name: string;
  path: string;
  type: 'file' | 'folder' | 'module';
  parts?: CoursePart[];
  files?: CourseFile[];
  size?: number;
  extension?: string;
  isVideo?: boolean;
  isDocument?: boolean;
  isImage?: boolean;
  fileType?: string;
}

const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v'];
const documentExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'];
const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'];

function getFileType(extension: string): { isVideo: boolean; isDocument: boolean; isImage: boolean; fileType: string } {
  const ext = extension.toLowerCase();
  return {
    isVideo: videoExtensions.includes(ext),
    isDocument: documentExtensions.includes(ext),
    isImage: imageExtensions.includes(ext),
    fileType: ext.substring(1) || 'unknown'
  };
}

function scanDirectory(dirPath: string, basePath: string = ''): CoursePart[] {
  try {
    const items = fs.readdirSync(dirPath);
    const parts: CoursePart[] = [];

    for (const item of items) {
      // Skip hidden files, system files, and extracted text directories
      if (item.startsWith('.') || 
          item === 'Thumbs.db' || 
          item === 'desktop.ini' || 
          item === '_extracted_texts' ||
          item.startsWith('_extracted_')) {
        continue;
      }

      const fullPath = path.join(dirPath, item);
      const relativePath = path.posix.join(basePath, item);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        // This is a folder, scan it recursively
        const subParts = scanDirectory(fullPath, relativePath);
        parts.push({
          name: item,
          path: relativePath,
          type: 'folder',
          parts: subParts
        });
      } else {
        // This is a file
        const extension = path.extname(item);
        const { isVideo, isDocument, isImage, fileType } = getFileType(extension);
        
        parts.push({
          name: item,
          path: relativePath,
          type: 'file',
          size: stats.size,
          extension,
          isVideo,
          isDocument,
          isImage,
          fileType
        });
      }
    }

    return parts.sort((a, b) => {
      // Sort folders first, then files
      if (a.type === 'folder' && b.type === 'file') return -1;
      if (a.type === 'file' && b.type === 'folder') return 1;
      return a.name.localeCompare(b.name);
    });
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error);
    return [];
  }
}

export async function GET() {
  try {
    const courseDirectory = path.join(process.cwd(), '..');
    
    if (!fs.existsSync(courseDirectory)) {
      return NextResponse.json({ error: 'Course directory not found' }, { status: 404 });
    }

    // Scan for modules based on predefined patterns
    const modules = [];
    let totalFiles = 0;
    let totalVideos = 0;
    let totalDocuments = 0;

    for (const predefinedModule of PREDEFINED_MODULES) {
      const modulePath = path.join(courseDirectory, predefinedModule.folderPattern);
      
      if (fs.existsSync(modulePath)) {
        const parts = scanDirectory(modulePath, predefinedModule.folderPattern);
        
        // Count files recursively
        const countFiles = (partsList: CoursePart[]): void => {
          for (const part of partsList) {
            if (part.type === 'file') {
              totalFiles++;
              if (part.isVideo) totalVideos++;
              if (part.isDocument) totalDocuments++;
            } else if (part.parts) {
              countFiles(part.parts);
            }
          }
        };
        
        countFiles(parts);

        modules.push({
          type: "module",
          name: predefinedModule.name,
          path: predefinedModule.folderPattern,
          parts,
          id: predefinedModule.id,
          description: predefinedModule.description,
          color: predefinedModule.color
        });
      } else {
        // Module folder doesn't exist, create empty structure
        modules.push({
          type: "module",
          name: predefinedModule.name,
          path: predefinedModule.folderPattern,
          parts: [],
          id: predefinedModule.id,
          description: predefinedModule.description,
          color: predefinedModule.color
        });
      }
    }

    const courseStructure = {
      title: "Photon Trading Zero to Funded 4.0 (2025)",
      modules: modules.sort((a, b) => a.id - b.id),
      totalModules: PREDEFINED_MODULES.length,
      totalFiles,
      totalVideos,
      totalDocuments,
      estimatedDuration: Math.round(totalVideos * 30), // Estimate 30 min per video
      lastScanned: new Date().toISOString()
    };

    // Save the scanned structure for future use
    const outputPath = path.join(process.cwd(), 'course-structure.json');
    fs.writeFileSync(outputPath, JSON.stringify(courseStructure, null, 2));

    return NextResponse.json(courseStructure);
  } catch (error) {
    console.error('Error scanning course structure:', error);
    return NextResponse.json(
      { error: 'Failed to scan course structure' },
      { status: 500 }
    );
  }
}