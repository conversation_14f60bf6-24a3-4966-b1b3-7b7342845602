# Photon Trading Course Website

A beautiful, feature-rich Next.js application for delivering trading course content with AI-powered video analysis, progress tracking, and community features.

## Features

### 🎨 Modern UI/UX
- **Liquid Glass Design**: Beautiful glassmorphism effects with gradient backgrounds
- **Responsive Layout**: Fully responsive design that works on all devices
- **Dark Theme**: Eye-friendly dark theme optimized for learning
- **Smooth Animations**: Framer Motion powered animations for enhanced UX

### 🔐 Authentication System
- **Password Protection**: Secure course access with password authentication
- **User Management**: Username-based user identification and progress tracking
- **Persistent Sessions**: Remember user login state across browser sessions

### 📚 Course Content Management
- **Automatic Structure Analysis**: Scans local course folders and generates navigation
- **Multi-format Support**: Videos (.mp4), Documents (.pdf, .docx), Images (.png, .jpg)
- **Hierarchical Navigation**: Module-based organization with collapsible sections
- **Smart File Serving**: Secure file serving with range request support for video streaming

### 🎥 Advanced Video Player
- **Custom Controls**: Play/pause, seek, volume, fullscreen, PiP support
- **Multiple Playback Speeds**: 0.5x to 2x speed control
- **Timestamp Sharing**: Share videos at specific timestamps
- **Subtitle Support**: AI-generated captions from video analysis
- **Keyboard Shortcuts**: Space for play/pause, arrow keys for seeking

### 🤖 AI-Powered Analysis (Google Gemini)
- **Automatic Video Analysis**: Generate transcripts, summaries, and insights
- **Concept Maps**: Visual representation of video concepts and relationships
- **Glossary Generation**: Automatic creation of trading term definitions
- **Slide Summaries**: Key points extracted as slide-style summaries
- **Cheat Sheets**: Important formulas and rules extracted from content

### 📊 Progress Tracking & Analytics
- **Personal Progress**: Track watched videos, completed modules, and time spent
- **Learning Analytics**: Visual charts showing learning patterns and velocity
- **Community Leaderboard**: Compare progress with other learners
- **Streak Tracking**: Maintain learning streaks and motivation
- **Session Analytics**: Detailed statistics on learning sessions

### 📝 Note-Taking System
- **Universal Notes**: Take notes accessible throughout the platform
- **Video-Linked Notes**: Notes tied to specific videos and timestamps
- **Keyboard Shortcuts**: Quick note access with Ctrl+/
- **Persistent Storage**: Notes saved locally and synced across sessions

### 🎯 Smart Features
- **Continue Where You Left Off**: Resume from your last position
- **Smart Recommendations**: Next suggested content based on progress
- **Search Functionality**: Quick search through course content
- **Keyboard Navigation**: Full keyboard support for power users

## Setup Instructions

### Prerequisites
- Node.js 18.0 or higher
- npm or yarn
- Google Gemini API key (for AI features)

### Installation

1. **Navigate to the project directory**
   ```bash
   cd "Photon Trading Zero to Funded 4.0 (2025) @Trading_Tuts/trading-course-website"
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Update `.env.local` with your configuration:
   ```env
   GEMINI_API_KEY=your_actual_api_key_here
   NEXT_PUBLIC_COURSE_PASSWORD=your_custom_password
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

5. **Access the Application**
   Open [http://localhost:3000](http://localhost:3000)
   
   **Password**: Use the password set in your `.env.local` file

## Course Structure

The application automatically analyzes your course folder structure. Ensure your content follows this pattern:

```
Photon Trading Zero to Funded 4.0 (2025) @Trading_Tuts/
├── 1. Start Here/
│   ├── 1. Welcome To Photon.ONEDDL.mp4
│   ├── 1. Welcome To Photon.ONEDDL.docx
│   └── ...
├── 2. Intro To FX/
│   └── ...
└── trading-course-website/
    └── (Next.js app files)
```

## Usage Guide

### Getting Started
1. Enter the course password (configured in `.env.local`)
2. Provide your username for progress tracking
3. Explore the dashboard to see course overview and progress

### Keyboard Shortcuts
- `Space`: Play/pause video
- `←/→`: Seek backward/forward 10 seconds
- `Ctrl+/`: Toggle notes panel
- `Ctrl+B`: Toggle sidebar
- `Escape`: Close current view
- `F`: Toggle fullscreen

### AI Video Analysis
1. Open any video in the course
2. Click "Generate Analysis" button
3. Wait for AI processing (may take 1-2 minutes)
4. View generated transcript, concept maps, and summaries

## Technical Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **UI Components**: ShadCN UI, Framer Motion
- **AI Integration**: Google Gemini API
- **Analytics**: Recharts for data visualization
- **Storage**: Local storage for user data

## Features in Detail

### Authentication Flow
- Password-based initial authentication
- Username collection for personalization
- Persistent login state

### Video Player Features
- Custom-built with HTML5 video API
- Supports video streaming with range requests
- Picture-in-picture mode
- Speed control (0.5x to 2x)
- Timestamp sharing
- AI-generated captions

### Analytics Dashboard
- Personal progress tracking
- Learning velocity charts
- Community comparison
- Session statistics
- Module completion rates

### Notes System
- Universal notes across all content
- Video-timestamp linking
- Keyboard shortcuts for quick access
- Local storage persistence

## API Endpoints

- `GET /api/course-structure`: Returns course navigation structure
- `POST /api/analyze-video`: Processes video with AI analysis
- `GET /api/serve-file`: Serves course content files securely

## Troubleshooting

### Common Issues

1. **Videos not loading**: Check file paths and ensure course structure is correct
2. **AI analysis failing**: Verify Gemini API key and internet connection
3. **Progress not saving**: Check browser local storage permissions

### Performance Tips

- Large videos may take time to analyze with AI
- Keep browser tabs open for better progress tracking
- Clear browser cache if experiencing issues

## Security Features

- Secure file serving with path validation
- API key protection in environment variables
- No sensitive data exposure in client-side code

## License

Educational use only. Respect original course content licensing.
