Extracted from: 5. How To Backtest & Record Data Efficiently.ONEDDL.pdf
Original file: 9.  Testing/5. How To Backtest & Record Data Efficiently.ONEDDL.pdf
Extraction date: 2025-07-06T09:52:18.235Z
File type: PDF
================================================================================

How To Backtest & Record Data Efficiently
9. ￿ TESTING
 RECOMMENDED BACKTESTING PROCESS
BEGINNER -> Backtest without bar replay - use the benefit of hindsight
EXPERIENCED -> Use bar replay / FX Replay
 
1. Backtest one month WITHOUT data entry
(Never backtest more than one month in a day)
Quality > Quantity
2. Data entry at the end of the month
Visually review all price action
-> look for mistakes
-> look for patterns in wins/losses
3. Repeat (next day)
 
DATA ENTRY
Use the measure tool on Tradingview to easily and quickly measure the 3 inputs you need for 
the spreadsheet.
You can hold down 'shift' + "click on the chart' to quickly use this tool. 
Magnet mode will help to snap the tool exactly to the high and low of the candle so it measures 
it accurately.

ENTRY CANDLE SIZE:
Measure the size of the candle that you enter on. 
 
MAE SL BUFFER:
MAE = maximum adverse excursion
This is how far price trades behind the entry candle.
One of three scenarios:
1) Price doesn't trade behind your entry candle -> type '0'
2) Price trades a few pips behind your entry candle -> type 'x' number of pips that it trades 
behind
3) Price trades very far behind your entry candle and you know you're never going to have a SL 
buffer that big so it will always be a losing trade for you = type '-'
 
MFE
Maximum Favourable Excursion = how many pips price trades in your favour from your entry 
price level
If you will never hold a trade over the night/weekend, then simply measure how far price went 
until the time you would close the trade in the live market.