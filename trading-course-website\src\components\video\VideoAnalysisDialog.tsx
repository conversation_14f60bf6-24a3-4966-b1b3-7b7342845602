'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { VideoAnalysis } from '@/types/course';
import ConceptMap from '@/components/visualization/ConceptMap';

interface VideoAnalysisDialogProps {
  analysis: VideoAnalysis;
  isOpen: boolean;
  onClose: () => void;
  initialTab?: 'overview' | 'concept_map' | 'slides' | 'transcript' | 'glossary' | 'cheat_sheet';
}

export default function VideoAnalysisDialog({ 
  analysis, 
  isOpen, 
  onClose, 
  initialTab = 'overview' 
}: VideoAnalysisDialogProps) {
  const [activeTab, setActiveTab] = useState(initialTab);

  const tabs = [
    { id: 'overview', label: 'Overview', content: analysis.overview },
    { id: 'concept_map', label: 'Concept Map', content: null },
    { id: 'slides', label: 'Slides', content: analysis.slides },
    { id: 'transcript', label: 'Transcript', content: analysis.transcript },
    { id: 'glossary', label: 'Glossary', content: analysis.glossary },
    { id: 'cheat_sheet', label: 'Cheat Sheet', content: analysis.cheat_sheet }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="prose prose-invert max-w-none">
            <div className="whitespace-pre-wrap text-gray-300 leading-relaxed">
              {analysis.overview}
            </div>
          </div>
        );
      
      case 'concept_map':
        return (
          <div className="h-96">
            <ConceptMap conceptMap={analysis.concept_map} />
          </div>
        );
      
      case 'slides':
        return (
          <div className="space-y-4">
            {analysis.slides.map((slide, index) => (
              <div key={index} className="bg-gray-800/50 p-4 rounded-lg border border-gray-600">
                <h3 className="text-white font-semibold mb-2 text-lg">
                  {index + 1}. {slide.title}
                </h3>
                <div className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {slide.content}
                </div>
              </div>
            ))}
          </div>
        );

      case 'transcript':
        return (
          <div className="prose prose-invert max-w-none">
            <div className="whitespace-pre-wrap text-gray-300 leading-relaxed font-mono text-sm">
              {analysis.transcript}
            </div>
          </div>
        );

      case 'glossary':
        return (
          <div className="space-y-3">
            {analysis.glossary.map((item, index) => (
              <div key={index} className="bg-gray-800/50 p-4 rounded-lg border border-gray-600">
                <h3 className="text-white font-semibold mb-2">{item.term}</h3>
                <div className="text-gray-300 leading-relaxed">{item.definition}</div>
              </div>
            ))}
          </div>
        );

      case 'cheat_sheet':
        return (
          <div className="space-y-2">
            {analysis.cheat_sheet?.map((item, index) => (
              <div key={index} className="flex items-start space-x-3 bg-gray-800/50 p-3 rounded-lg">
                <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {index + 1}
                </div>
                <div className="text-gray-300 leading-relaxed">{item}</div>
              </div>
            )) || <div className="text-gray-400 text-center py-8">No cheat sheet available</div>}
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] bg-gray-900 border-gray-700 text-white overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">
            Video Analysis - {tabs.find(tab => tab.id === activeTab)?.label}
          </DialogTitle>
        </DialogHeader>
        
        {/* Tab Navigation */}
        <div className="flex space-x-1 border-b border-gray-700">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "ghost"}
              onClick={() => setActiveTab(tab.id as any)}
              className={`rounded-none border-b-2 ${
                activeTab === tab.id
                  ? "border-blue-500 bg-blue-500/20 text-white"
                  : "border-transparent text-gray-400 hover:text-white hover:bg-gray-800"
              }`}
            >
              {tab.label}
            </Button>
          ))}
        </div>
        
        {/* Content Area */}
        <div className="flex-1 overflow-y-auto p-4 bg-gray-800/50 rounded-lg">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
