{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"glass-morphism-dark text-white border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-300\",\n        destructive:\n          \"glass-morphism-dark text-white border border-red-400/30 bg-red-500/20 hover:bg-red-500/30 hover:border-red-400/50\",\n        outline:\n          \"glass-morphism-dark text-white border border-white/20 hover:bg-white/10 hover:border-white/30\",\n        secondary:\n          \"glass-morphism-dark text-white border border-white/15 bg-white/5 hover:bg-white/10 hover:border-white/25\",\n        ghost:\n          \"text-white hover:bg-white/10 backdrop-blur-sm transition-all duration-300\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/visualization/ConceptMap.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef } from 'react';\r\nimport { VideoAnalysis } from '@/types/course';\r\n\r\ninterface ConceptMapProps {\r\n  conceptMap: VideoAnalysis['concept_map'];\r\n}\r\n\r\nexport default function ConceptMap({ conceptMap }: ConceptMapProps) {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (!conceptMap || !canvasRef.current) return;\r\n\r\n    const canvas = canvasRef.current;\r\n    const ctx = canvas.getContext('2d');\r\n    if (!ctx) return;\r\n\r\n    // Set canvas size\r\n    canvas.width = canvas.offsetWidth * window.devicePixelRatio;\r\n    canvas.height = canvas.offsetHeight * window.devicePixelRatio;\r\n    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);\r\n\r\n    const width = canvas.offsetWidth;\r\n    const height = canvas.offsetHeight;\r\n\r\n    // Clear canvas\r\n    ctx.clearRect(0, 0, width, height);\r\n\r\n    // Node positioning\r\n    const nodes = conceptMap.nodes.map((node, index) => ({\r\n      name: node,\r\n      x: (Math.cos((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (width / 2 - 80) + 80,\r\n      y: (Math.sin((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (height / 2 - 40) + 40,\r\n      radius: 30 + node.length * 2\r\n    }));\r\n\r\n    // Draw edges\r\n    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';\r\n    ctx.lineWidth = 2;\r\n    conceptMap.edges.forEach(edge => {\r\n      const fromNode = nodes.find(n => n.name === edge.from);\r\n      const toNode = nodes.find(n => n.name === edge.to);\r\n      \r\n      if (fromNode && toNode) {\r\n        ctx.beginPath();\r\n        ctx.moveTo(fromNode.x, fromNode.y);\r\n        ctx.lineTo(toNode.x, toNode.y);\r\n        ctx.stroke();\r\n\r\n        // Draw arrow\r\n        const angle = Math.atan2(toNode.y - fromNode.y, toNode.x - fromNode.x);\r\n        const arrowLength = 10;\r\n        ctx.beginPath();\r\n        ctx.moveTo(\r\n          toNode.x - toNode.radius * Math.cos(angle),\r\n          toNode.y - toNode.radius * Math.sin(angle)\r\n        );\r\n        ctx.lineTo(\r\n          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle - 0.5),\r\n          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle - 0.5)\r\n        );\r\n        ctx.moveTo(\r\n          toNode.x - toNode.radius * Math.cos(angle),\r\n          toNode.y - toNode.radius * Math.sin(angle)\r\n        );\r\n        ctx.lineTo(\r\n          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle + 0.5),\r\n          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle + 0.5)\r\n        );\r\n        ctx.stroke();\r\n\r\n        // Draw relation label\r\n        const midX = (fromNode.x + toNode.x) / 2;\r\n        const midY = (fromNode.y + toNode.y) / 2;\r\n        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';\r\n        ctx.font = '10px \"Fira Code\"';\r\n        ctx.textAlign = 'center';\r\n        ctx.fillText(edge.relation, midX, midY - 5);\r\n      }\r\n    });\r\n\r\n    // Draw nodes\r\n    nodes.forEach((node, index) => {\r\n      // Node background\r\n      const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, node.radius);\r\n      gradient.addColorStop(0, `hsla(${200 + index * 30}, 70%, 60%, 0.8)`);\r\n      gradient.addColorStop(1, `hsla(${200 + index * 30}, 70%, 40%, 0.6)`);\r\n      \r\n      ctx.fillStyle = gradient;\r\n      ctx.beginPath();\r\n      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);\r\n      ctx.fill();\r\n\r\n      // Node border\r\n      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';\r\n      ctx.lineWidth = 2;\r\n      ctx.stroke();\r\n\r\n      // Node text\r\n      ctx.fillStyle = '#ffffff';\r\n      ctx.font = '12px \"Fira Code\"';\r\n      ctx.textAlign = 'center';\r\n      ctx.textBaseline = 'middle';\r\n      \r\n      // Wrap text for long nodes\r\n      const words = node.name.split(' ');\r\n      if (words.length > 2) {\r\n        const line1 = words.slice(0, Math.ceil(words.length / 2)).join(' ');\r\n        const line2 = words.slice(Math.ceil(words.length / 2)).join(' ');\r\n        ctx.fillText(line1, node.x, node.y - 6);\r\n        ctx.fillText(line2, node.x, node.y + 6);\r\n      } else {\r\n        ctx.fillText(node.name, node.x, node.y);\r\n      }\r\n    });\r\n\r\n  }, [conceptMap]);\r\n\r\n  if (!conceptMap || conceptMap.nodes.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-8 text-white/60\">\r\n        <p>No concept map available</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full h-64 relative\">\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"w-full h-full\"\r\n        style={{ width: '100%', height: '100%' }}\r\n      />\r\n      <div className=\"absolute bottom-2 left-2 text-white/60 text-xs\">\r\n        Interactive Concept Map\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,WAAW,EAAE,UAAU,EAAmB;;IAChE,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,cAAc,CAAC,UAAU,OAAO,EAAE;YAEvC,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,kBAAkB;YAClB,OAAO,KAAK,GAAG,OAAO,WAAW,GAAG,OAAO,gBAAgB;YAC3D,OAAO,MAAM,GAAG,OAAO,YAAY,GAAG,OAAO,gBAAgB;YAC7D,IAAI,KAAK,CAAC,OAAO,gBAAgB,EAAE,OAAO,gBAAgB;YAE1D,MAAM,QAAQ,OAAO,WAAW;YAChC,MAAM,SAAS,OAAO,YAAY;YAElC,eAAe;YACf,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;YAE3B,mBAAmB;YACnB,MAAM,QAAQ,WAAW,KAAK,CAAC,GAAG;8CAAC,CAAC,MAAM,QAAU,CAAC;wBACnD,MAAM;wBACN,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,IAAI,KAAK,EAAE,GAAI,WAAW,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,IAAI;wBACxF,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,IAAI,KAAK,EAAE,GAAI,WAAW,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI;wBACzF,QAAQ,KAAK,KAAK,MAAM,GAAG;oBAC7B,CAAC;;YAED,aAAa;YACb,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,WAAW,KAAK,CAAC,OAAO;wCAAC,CAAA;oBACvB,MAAM,WAAW,MAAM,IAAI;yDAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI;;oBACrD,MAAM,SAAS,MAAM,IAAI;uDAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,EAAE;;oBAEjD,IAAI,YAAY,QAAQ;wBACtB,IAAI,SAAS;wBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;wBACjC,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;wBAC7B,IAAI,MAAM;wBAEV,aAAa;wBACb,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;wBACrE,MAAM,cAAc;wBACpB,IAAI,SAAS;wBACb,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,QACpC,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC;wBAEtC,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ,MAC5E,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ;wBAE9E,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,QACpC,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC;wBAEtC,IAAI,MAAM,CACR,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ,MAC5E,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,KAAK,GAAG,CAAC,QAAQ;wBAE9E,IAAI,MAAM;wBAEV,sBAAsB;wBACtB,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI;wBACvC,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI;wBACvC,IAAI,SAAS,GAAG;wBAChB,IAAI,IAAI,GAAG;wBACX,IAAI,SAAS,GAAG;wBAChB,IAAI,QAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM,OAAO;oBAC3C;gBACF;;YAEA,aAAa;YACb,MAAM,OAAO;wCAAC,CAAC,MAAM;oBACnB,kBAAkB;oBAClB,MAAM,WAAW,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM;oBACxF,SAAS,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC;oBACnE,SAAS,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC;oBAEnE,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,EAAE,GAAG,IAAI,KAAK,EAAE;oBACnD,IAAI,IAAI;oBAER,cAAc;oBACd,IAAI,WAAW,GAAG;oBAClB,IAAI,SAAS,GAAG;oBAChB,IAAI,MAAM;oBAEV,YAAY;oBACZ,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI,GAAG;oBACX,IAAI,SAAS,GAAG;oBAChB,IAAI,YAAY,GAAG;oBAEnB,2BAA2B;oBAC3B,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC;oBAC9B,IAAI,MAAM,MAAM,GAAG,GAAG;wBACpB,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC;wBAC/D,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC;wBAC5D,IAAI,QAAQ,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG;wBACrC,IAAI,QAAQ,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG;oBACvC,OAAO;wBACL,IAAI,QAAQ,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;oBACxC;gBACF;;QAEF;+BAAG;QAAC;KAAW;IAEf,IAAI,CAAC,cAAc,WAAW,KAAK,CAAC,MAAM,KAAK,GAAG;QAChD,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;;;;;;0BAEzC,6LAAC;gBAAI,WAAU;0BAAiD;;;;;;;;;;;;AAKtE;GAnIwB;KAAA", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/video/CustomVideoPlayer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef } from 'react';\r\nimport { VideoAnalysis } from '@/types/course';\r\nimport ConceptMap from '@/components/visualization/ConceptMap';\r\n\r\ninterface CustomVideoPlayerProps {\r\n  src: string;\r\n  analysis?: VideoAnalysis;\r\n  onTimeUpdate?: (currentTime: number) => void;\r\n  onProgress?: (progress: number) => void;\r\n  initialTime?: number;\r\n}\r\n\r\nexport default function CustomVideoPlayer({\r\n  src,\r\n  analysis,\r\n  onTimeUpdate,\r\n  onProgress,\r\n  initialTime = 0\r\n}: CustomVideoPlayerProps) {\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  \r\n  console.log('🎬 ===== VIDEO PLAYER COMPONENT RENDER =====');\r\n  console.log('📥 Video src prop:', JSON.stringify(src));\r\n  console.log('🕐 Render timestamp:', new Date().toISOString());\r\n\r\n  useEffect(() => {\r\n    console.log('📺 Video useEffect triggered');\r\n    const video = videoRef.current;\r\n    if (!video) {\r\n      console.log('❌ No video ref found');\r\n      return;\r\n    }\r\n    \r\n    console.log('✅ Video element found, adding event listeners');\r\n    console.log('📋 Video element src:', video.src);\r\n    console.log('📋 Video element currentSrc:', video.currentSrc);\r\n\r\n    const handleTimeUpdate = () => {\r\n      onTimeUpdate?.(video.currentTime);\r\n    };\r\n\r\n    const handleProgress = () => {\r\n      if (video.duration > 0) {\r\n        const progress = (video.currentTime / video.duration) * 100;\r\n        onProgress?.(progress);\r\n      }\r\n    };\r\n\r\n    const handleLoadedMetadata = () => {\r\n      console.log('📺 Video metadata loaded');\r\n      console.log('⏱️ Duration:', video.duration);\r\n      if (initialTime > 0) {\r\n        video.currentTime = initialTime;\r\n      }\r\n    };\r\n\r\n    const handleLoadStart = () => {\r\n      console.log('🔄 Video load started');\r\n      console.log('📋 Current src:', video.currentSrc);\r\n    };\r\n\r\n    const handleCanPlay = () => {\r\n      console.log('✅ Video can play');\r\n    };\r\n\r\n    const handleCanPlayThrough = () => {\r\n      console.log('✅ Video can play through');\r\n    };\r\n\r\n    const handleError = (e: Event) => {\r\n      console.log('❌ Video error occurred:');\r\n      console.log('📋 Error event:', e);\r\n      console.log('📋 Video error code:', video.error?.code);\r\n      console.log('📋 Video error message:', video.error?.message);\r\n      console.log('📋 Video src:', video.src);\r\n      console.log('📋 Video currentSrc:', video.currentSrc);\r\n    };\r\n\r\n    const handleWaiting = () => {\r\n      console.log('⏳ Video waiting for data');\r\n    };\r\n\r\n    const handleStalled = () => {\r\n      console.log('🚫 Video stalled');\r\n    };\r\n\r\n    video.addEventListener('timeupdate', handleTimeUpdate);\r\n    video.addEventListener('progress', handleProgress);\r\n    video.addEventListener('loadedmetadata', handleLoadedMetadata);\r\n    video.addEventListener('loadstart', handleLoadStart);\r\n    video.addEventListener('canplay', handleCanPlay);\r\n    video.addEventListener('canplaythrough', handleCanPlayThrough);\r\n    video.addEventListener('error', handleError);\r\n    video.addEventListener('waiting', handleWaiting);\r\n    video.addEventListener('stalled', handleStalled);\r\n\r\n    return () => {\r\n      console.log('🧹 Cleaning up video event listeners');\r\n      video.removeEventListener('timeupdate', handleTimeUpdate);\r\n      video.removeEventListener('progress', handleProgress);\r\n      video.removeEventListener('loadedmetadata', handleLoadedMetadata);\r\n      video.removeEventListener('loadstart', handleLoadStart);\r\n      video.removeEventListener('canplay', handleCanPlay);\r\n      video.removeEventListener('canplaythrough', handleCanPlayThrough);\r\n      video.removeEventListener('error', handleError);\r\n      video.removeEventListener('waiting', handleWaiting);\r\n      video.removeEventListener('stalled', handleStalled);\r\n    };\r\n  }, [onTimeUpdate, onProgress, initialTime]);\r\n\r\n  const jumpToTimestamp = (timestamp: string) => {\r\n    const video = videoRef.current;\r\n    if (!video) return;\r\n\r\n    const timeMatch = timestamp.match(/(\\d+):(\\d+)/);\r\n    if (timeMatch) {\r\n      const minutes = parseInt(timeMatch[1]);\r\n      const seconds = parseInt(timeMatch[2]);\r\n      const time = minutes * 60 + seconds;\r\n      video.currentTime = time;\r\n    }\r\n  };\r\n\r\n  console.log('🎨 About to render video element with src:', src);\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Debug Panel */}\r\n      <div className=\"bg-gray-900 border border-gray-700 rounded-lg p-4 mb-4\">\r\n        <h3 className=\"text-white font-bold mb-2\">🐛 Debug Information</h3>\r\n        <div className=\"text-sm text-gray-300 space-y-1\">\r\n          <div><strong>Video Source:</strong> {src}</div>\r\n          <div><strong>Initial Time:</strong> {initialTime}</div>\r\n          <div><strong>Analysis Available:</strong> {analysis ? 'Yes' : 'No'}</div>\r\n          <div><strong>Port Check:</strong> <a href={src} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-400 hover:text-blue-300\">Test Direct Link</a></div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"relative video-player-container\">\r\n        <video\r\n          ref={videoRef}\r\n          className=\"w-full h-auto bg-black rounded-lg\"\r\n          controls\r\n          preload=\"metadata\"\r\n          style={{ maxHeight: '60vh' }}\r\n          onLoadStart={() => console.log('🎬 Video onLoadStart triggered')}\r\n          onError={(e) => console.log('🎬 Video onError triggered:', e)}\r\n          onCanPlay={() => console.log('🎬 Video onCanPlay triggered')}\r\n        >\r\n          <source src={src} type=\"video/mp4\" />\r\n          <p className=\"text-white p-4\">\r\n            Your browser does not support the video tag. \r\n            <a href={src} download className=\"text-blue-400 hover:text-blue-300 ml-2\">\r\n              Download video instead\r\n            </a>\r\n          </p>\r\n        </video>\r\n      </div>\r\n\r\n      {analysis && (\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n          <div className=\"space-y-4\">\r\n            <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n              <h3 className=\"text-white font-medium mb-2\">Video Overview</h3>\r\n              <p className=\"text-white/80 text-sm\">{analysis.overview}</p>\r\n            </div>\r\n\r\n            <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n              <h3 className=\"text-white font-medium mb-2\">Quick Note</h3>\r\n              <p className=\"text-white/80 text-sm\">{analysis.quick_note}</p>\r\n            </div>\r\n\r\n            {analysis.slides && analysis.slides.length > 0 && (\r\n              <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n                <h3 className=\"text-white font-medium mb-3\">Key Slides</h3>\r\n                <div className=\"space-y-3 max-h-60 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.slides.map((slide, index) => (\r\n                    <div key={index} className=\"border-l-2 border-blue-400 pl-3 py-2\">\r\n                      <h4 className=\"text-white text-sm font-medium\">{slide.title}</h4>\r\n                      <p className=\"text-white/70 text-xs mt-1\">{slide.content}</p>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            {analysis.concept_map && (\r\n              <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n                <h3 className=\"text-white font-medium mb-3\">Concept Map</h3>\r\n                <ConceptMap conceptMap={analysis.concept_map} />\r\n              </div>\r\n            )}\r\n\r\n            {analysis.glossary && analysis.glossary.length > 0 && (\r\n              <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n                <h3 className=\"text-white font-medium mb-3\">Glossary</h3>\r\n                <div className=\"space-y-2 max-h-60 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.glossary.map((item, index) => (\r\n                    <div key={index} className=\"p-3 bg-white/5 rounded-lg\">\r\n                      <h4 className=\"text-white font-medium text-sm\">{item.term}</h4>\r\n                      <p className=\"text-white/70 text-xs mt-1\">{item.definition}</p>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            {analysis.cheat_sheet && analysis.cheat_sheet.length > 0 && (\r\n              <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n                <h3 className=\"text-white font-medium mb-3\">Quick Reference</h3>\r\n                <div className=\"space-y-2 max-h-40 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.cheat_sheet.map((item, index) => (\r\n                    <div key={index} className=\"text-white/80 text-xs p-2 bg-white/5 rounded\">\r\n                      {item}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {analysis.transcript && (\r\n              <div className=\"glass-morphism-dark p-4 border border-white/20 rounded-lg\">\r\n                <h3 className=\"text-white font-medium mb-3\">Transcript Timeline</h3>\r\n                <div className=\"space-y-2 max-h-60 overflow-y-auto custom-scrollbar\">\r\n                  {analysis.transcript.split('[').filter(Boolean).slice(0, 10).map((section, index) => {\r\n                    const timestampMatch = section.match(/^(\\d+:\\d+)\\]/);\r\n                    if (timestampMatch) {\r\n                      const timestamp = timestampMatch[1];\r\n                      const text = section.replace(/^\\d+:\\d+\\]/, '').trim();\r\n                      \r\n                      return (\r\n                        <div \r\n                          key={index} \r\n                          className=\"flex items-start space-x-2 cursor-pointer hover:bg-white/5 p-2 rounded\"\r\n                          onClick={() => jumpToTimestamp(timestamp)}\r\n                        >\r\n                          <span className=\"text-blue-400 text-xs font-mono min-w-[40px]\">\r\n                            {timestamp}\r\n                          </span>\r\n                          <span className=\"text-white/80 text-xs\">\r\n                            {text.substring(0, 100)}...\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    }\r\n                    return null;\r\n                  })}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAce,SAAS,kBAAkB,EACxC,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,cAAc,CAAC,EACQ;;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,sBAAsB,KAAK,SAAS,CAAC;IACjD,QAAQ,GAAG,CAAC,wBAAwB,IAAI,OAAO,WAAW;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,QAAQ,GAAG,CAAC;YACZ,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,yBAAyB,MAAM,GAAG;YAC9C,QAAQ,GAAG,CAAC,gCAAgC,MAAM,UAAU;YAE5D,MAAM;gEAAmB;oBACvB,eAAe,MAAM,WAAW;gBAClC;;YAEA,MAAM;8DAAiB;oBACrB,IAAI,MAAM,QAAQ,GAAG,GAAG;wBACtB,MAAM,WAAW,AAAC,MAAM,WAAW,GAAG,MAAM,QAAQ,GAAI;wBACxD,aAAa;oBACf;gBACF;;YAEA,MAAM;oEAAuB;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,gBAAgB,MAAM,QAAQ;oBAC1C,IAAI,cAAc,GAAG;wBACnB,MAAM,WAAW,GAAG;oBACtB;gBACF;;YAEA,MAAM;+DAAkB;oBACtB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,UAAU;gBACjD;;YAEA,MAAM;6DAAgB;oBACpB,QAAQ,GAAG,CAAC;gBACd;;YAEA,MAAM;oEAAuB;oBAC3B,QAAQ,GAAG,CAAC;gBACd;;YAEA,MAAM;2DAAc,CAAC;oBACnB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,mBAAmB;oBAC/B,QAAQ,GAAG,CAAC,wBAAwB,MAAM,KAAK,EAAE;oBACjD,QAAQ,GAAG,CAAC,2BAA2B,MAAM,KAAK,EAAE;oBACpD,QAAQ,GAAG,CAAC,iBAAiB,MAAM,GAAG;oBACtC,QAAQ,GAAG,CAAC,wBAAwB,MAAM,UAAU;gBACtD;;YAEA,MAAM;6DAAgB;oBACpB,QAAQ,GAAG,CAAC;gBACd;;YAEA,MAAM;6DAAgB;oBACpB,QAAQ,GAAG,CAAC;gBACd;;YAEA,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,YAAY;YACnC,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,aAAa;YACpC,MAAM,gBAAgB,CAAC,WAAW;YAClC,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,WAAW;YAClC,MAAM,gBAAgB,CAAC,WAAW;YAElC;+CAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,YAAY;oBACtC,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,aAAa;oBACvC,MAAM,mBAAmB,CAAC,WAAW;oBACrC,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,WAAW;oBACrC,MAAM,mBAAmB,CAAC,WAAW;gBACvC;;QACF;sCAAG;QAAC;QAAc;QAAY;KAAY;IAE1C,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,YAAY,UAAU,KAAK,CAAC;QAClC,IAAI,WAAW;YACb,MAAM,UAAU,SAAS,SAAS,CAAC,EAAE;YACrC,MAAM,UAAU,SAAS,SAAS,CAAC,EAAE;YACrC,MAAM,OAAO,UAAU,KAAK;YAC5B,MAAM,WAAW,GAAG;QACtB;IACF;IAEA,QAAQ,GAAG,CAAC,8CAA8C;IAE1D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4B;;;;;;kCAC1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAAsB;oCAAE;;;;;;;0CACrC,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAAsB;oCAAE;;;;;;;0CACrC,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAA4B;oCAAE,WAAW,QAAQ;;;;;;;0CAC9D,6LAAC;;kDAAI,6LAAC;kDAAO;;;;;;oCAAoB;kDAAC,6LAAC;wCAAE,MAAM;wCAAK,QAAO;wCAAS,KAAI;wCAAsB,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;0BAI5I,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,KAAK;oBACL,WAAU;oBACV,QAAQ;oBACR,SAAQ;oBACR,OAAO;wBAAE,WAAW;oBAAO;oBAC3B,aAAa,IAAM,QAAQ,GAAG,CAAC;oBAC/B,SAAS,CAAC,IAAM,QAAQ,GAAG,CAAC,+BAA+B;oBAC3D,WAAW,IAAM,QAAQ,GAAG,CAAC;;sCAE7B,6LAAC;4BAAO,KAAK;4BAAK,MAAK;;;;;;sCACvB,6LAAC;4BAAE,WAAU;;gCAAiB;8CAE5B,6LAAC;oCAAE,MAAM;oCAAK,QAAQ;oCAAC,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;YAO/E,0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDAAyB,SAAS,QAAQ;;;;;;;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDAAyB,SAAS,UAAU;;;;;;;;;;;;4BAG1D,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,mBAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAG,WAAU;kEAAkC,MAAM,KAAK;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAA8B,MAAM,OAAO;;;;;;;+CAFhD;;;;;;;;;;;;;;;;;;;;;;kCAUpB,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,WAAW,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC,oJAAA,CAAA,UAAU;wCAAC,YAAY,SAAS,WAAW;;;;;;;;;;;;4BAI/C,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAG,WAAU;kEAAkC,KAAK,IAAI;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAA8B,KAAK,UAAU;;;;;;;+CAFlD;;;;;;;;;;;;;;;;;;;;;;kCAUpB,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,mBACrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;gDAAgB,WAAU;0DACxB;+CADO;;;;;;;;;;;;;;;;4BAQjB,SAAS,UAAU,kBAClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS;4CACzE,MAAM,iBAAiB,QAAQ,KAAK,CAAC;4CACrC,IAAI,gBAAgB;gDAClB,MAAM,YAAY,cAAc,CAAC,EAAE;gDACnC,MAAM,OAAO,QAAQ,OAAO,CAAC,cAAc,IAAI,IAAI;gDAEnD,qBACE,6LAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,gBAAgB;;sEAE/B,6LAAC;4DAAK,WAAU;sEACb;;;;;;sEAEH,6LAAC;4DAAK,WAAU;;gEACb,KAAK,SAAS,CAAC,GAAG;gEAAK;;;;;;;;mDARrB;;;;;4CAYX;4CACA,OAAO;wCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAvPwB;KAAA", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/components/document/DocumentViewer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Download, FileText, Eye } from 'lucide-react';\r\nimport { CourseFile } from '@/types/course';\r\n\r\ninterface DocumentViewerProps {\r\n  file: CourseFile;\r\n}\r\n\r\nexport default function DocumentViewer({ file }: DocumentViewerProps) {\r\n  const [content, setContent] = useState<string>('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n  const [extractedText, setExtractedText] = useState<string>('');\r\n  const [isExtracting, setIsExtracting] = useState(false);\r\n\r\n  const loadDocumentContent = async () => {\r\n    // Always try to extract text for better viewing\r\n    setIsExtracting(true);\r\n    setError('');\r\n    \r\n    try {\r\n      const response = await fetch('/api/extract-text', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ filePath: file.path })\r\n      });\r\n      \r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.success) {\r\n          setExtractedText(result.content);\r\n          \r\n          // For .txt files, also load the original content\r\n          if (file.extension === '.txt') {\r\n            setIsLoading(true);\r\n            try {\r\n              const fileResponse = await fetch(`/api/serve-file?path=${encodeURIComponent(file.path)}`);\r\n              if (fileResponse.ok) {\r\n                const text = await fileResponse.text();\r\n                setContent(text);\r\n              }\r\n            } catch (err) {\r\n              console.error('Error loading original text file:', err);\r\n            } finally {\r\n              setIsLoading(false);\r\n            }\r\n          }\r\n        } else {\r\n          setError('Failed to extract document content');\r\n        }\r\n      } else {\r\n        setError('Failed to extract document content');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error extracting text:', err);\r\n      setError('Error extracting document content');\r\n    } finally {\r\n      setIsExtracting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadDocumentContent();\r\n  }, [file.path]);\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    const mb = bytes / (1024 * 1024);\r\n    if (mb > 1000) return `${(mb / 1024).toFixed(1)} GB`;\r\n    return `${mb.toFixed(1)} MB`;\r\n  };\r\n\r\n  const downloadFile = async () => {\r\n    try {\r\n      const response = await fetch(`/api/serve-file?path=${encodeURIComponent(file.path)}`);\r\n      if (!response.ok) {\r\n        throw new Error('Download failed');\r\n      }\r\n      \r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = file.name;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (err) {\r\n      console.error('Download error:', err);\r\n      setError('Failed to download file');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <FileText size={24} className=\"text-blue-400\" />\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-white\">{file.name}</h2>\r\n            <p className=\"text-white/60 text-sm\">\r\n              {file.fileType.toUpperCase()} • {formatFileSize(file.size)}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {file.extension === '.pdf' && (\r\n            <Button\r\n              onClick={() => window.open(`/api/serve-file?path=${encodeURIComponent(file.path)}`, '_blank')}\r\n              variant=\"outline\"\r\n              className=\"border-white/20 text-white hover:bg-white/10\"\r\n            >\r\n              <Eye size={16} className=\"mr-2\" />\r\n              View PDF\r\n            </Button>\r\n          )}\r\n          <Button \r\n            onClick={downloadFile}\r\n            variant=\"outline\"\r\n            className=\"border-white/20 text-white hover:bg-white/10\"\r\n          >\r\n            <Download size={16} className=\"mr-2\" />\r\n            Download\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"glass-morphism-dark p-6 rounded-lg\">\r\n        {(isLoading || isExtracting) && (\r\n          <div className=\"flex items-center justify-center py-8\">\r\n            <div className=\"animate-spin w-6 h-6 border-2 border-white/30 border-t-white rounded-full\"></div>\r\n            <span className=\"ml-3 text-white/70\">\r\n              {isExtracting ? 'Extracting text...' : 'Loading document...'}\r\n            </span>\r\n          </div>\r\n        )}\r\n        \r\n        {error && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-white/30\" />\r\n            <p className=\"text-white/70 mb-2\">Unable to preview this document</p>\r\n            <p className=\"text-white/50 text-sm\">{error}</p>\r\n          </div>\r\n        )}\r\n        \r\n        {extractedText && (\r\n          <div className=\"text-white/80\">\r\n            <h3 className=\"text-lg font-medium mb-4 text-white\">Document Content</h3>\r\n            <div className=\"text-sm leading-relaxed bg-black/20 p-4 rounded-lg\">\r\n              {file.extension === '.txt' && content ? (\r\n                <pre className=\"whitespace-pre-wrap font-mono\">{content}</pre>\r\n              ) : (\r\n                <div className=\"whitespace-pre-wrap\">{extractedText}</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {file.extension === '.pdf' && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-blue-400\" />\r\n            <p className=\"text-white/80 mb-2\">PDF Document</p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              Click \"View PDF\" to open in a new tab or download to view locally.\r\n            </p>\r\n            <div className=\"flex justify-center space-x-4\">\r\n              <Button\r\n                onClick={() => window.open(`/api/serve-file?path=${encodeURIComponent(file.path)}`, '_blank')}\r\n                className=\"bg-blue-500 hover:bg-blue-600\"\r\n              >\r\n                <Eye size={16} className=\"mr-2\" />\r\n                View in Browser\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {file.extension === '.docx' && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-green-400\" />\r\n            <p className=\"text-white/80 mb-2\">Microsoft Word Document</p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              {file.name} ({formatFileSize(file.size)})\r\n            </p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              This document contains course materials. Download to view the full content.\r\n            </p>\r\n            <div className=\"flex justify-center space-x-4\">\r\n              <Button\r\n                onClick={downloadFile}\r\n                className=\"bg-green-500 hover:bg-green-600\"\r\n              >\r\n                <Download size={16} className=\"mr-2\" />\r\n                Download Document\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {!extractedText && !isLoading && !isExtracting && !error && (\r\n          <div className=\"text-center py-8\">\r\n            <FileText size={48} className=\"mx-auto mb-4 text-white/30\" />\r\n            <p className=\"text-white/70 mb-2\">{file.name}</p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              {file.fileType.toUpperCase()} file ({formatFileSize(file.size)})\r\n            </p>\r\n            <p className=\"text-white/60 text-sm mb-4\">\r\n              This file type cannot be previewed in the browser. Download to view on your computer.\r\n            </p>\r\n            <Button\r\n              onClick={downloadFile}\r\n              variant=\"outline\"\r\n              className=\"border-white/20 text-white hover:bg-white/10\"\r\n            >\r\n              <Download size={16} className=\"mr-2\" />\r\n              Download File\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAWe,SAAS,eAAe,EAAE,IAAI,EAAuB;;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,sBAAsB;QAC1B,gDAAgD;QAChD,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU,KAAK,IAAI;gBAAC;YAC7C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,IAAI,OAAO,OAAO,EAAE;oBAClB,iBAAiB,OAAO,OAAO;oBAE/B,iDAAiD;oBACjD,IAAI,KAAK,SAAS,KAAK,QAAQ;wBAC7B,aAAa;wBACb,IAAI;4BACF,MAAM,eAAe,MAAM,MAAM,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG;4BACxF,IAAI,aAAa,EAAE,EAAE;gCACnB,MAAM,OAAO,MAAM,aAAa,IAAI;gCACpC,WAAW;4BACb;wBACF,EAAE,OAAO,KAAK;4BACZ,QAAQ,KAAK,CAAC,qCAAqC;wBACrD,SAAU;4BACR,aAAa;wBACf;oBACF;gBACF,OAAO;oBACL,SAAS;gBACX;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC,KAAK,IAAI;KAAC;IAEd,MAAM,iBAAiB,CAAC;QACtB,MAAM,KAAK,QAAQ,CAAC,OAAO,IAAI;QAC/B,IAAI,KAAK,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QACpD,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;IAC9B;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG;YACpF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,KAAK,IAAI;YACzB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,KAAK,IAAI;;;;;;kDAC3D,6LAAC;wCAAE,WAAU;;4CACV,KAAK,QAAQ,CAAC,WAAW;4CAAG;4CAAI,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,SAAS,KAAK,wBAClB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG,EAAE;gCACpF,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;0CAItC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,aAAa,YAAY,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CACb,eAAe,uBAAuB;;;;;;;;;;;;oBAK5C,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;oBAIzC,+BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,KAAK,UAAU,wBAC5B,6LAAC;oCAAI,WAAU;8CAAiC;;;;;yDAEhD,6LAAC;oCAAI,WAAU;8CAAuB;;;;;;;;;;;;;;;;;oBAM7C,KAAK,SAAS,KAAK,wBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG,EAAE;oCACpF,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;oBAOzC,KAAK,SAAS,KAAK,yBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAE,WAAU;;oCACV,KAAK,IAAI;oCAAC;oCAAG,eAAe,KAAK,IAAI;oCAAE;;;;;;;0CAE1C,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;oBAO9C,CAAC,iBAAiB,CAAC,aAAa,CAAC,gBAAgB,CAAC,uBACjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,6LAAC;gCAAE,WAAU;0CAAsB,KAAK,IAAI;;;;;;0CAC5C,6LAAC;gCAAE,WAAU;;oCACV,KAAK,QAAQ,CAAC,WAAW;oCAAG;oCAAQ,eAAe,KAAK,IAAI;oCAAE;;;;;;;0CAEjE,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAtNwB;KAAA", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/app/course/%5B...module%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { useParams, useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Input } from '@/components/ui/input';\r\nimport CustomVideoPlayer from '@/components/video/CustomVideoPlayer';\r\nimport DocumentViewer from '@/components/document/DocumentViewer';\r\nimport { \r\n  ArrowLeft, \r\n  FileText, \r\n  PlayCircle, \r\n  Image as ImageIcon, \r\n  Download,\r\n  Search,\r\n  ChevronRight,\r\n  ChevronDown,\r\n  StickyNote,\r\n  Users,\r\n  BarChart,\r\n  Clock,\r\n  Target,\r\n  Folder,\r\n  FolderOpen\r\n} from 'lucide-react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { CourseStructure, CourseFile, CoursePart, UserProgress, VideoAnalysis } from '@/types/course';\r\nimport { useHotkeys } from 'react-hotkeys-hook';\r\n\r\nexport default function ModulePage() {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const [courseStructure, setCourseStructure] = useState<CourseStructure | null>(null);\r\n  const [currentModule, setCurrentModule] = useState<CoursePart | null>(null);\r\n  const [selectedPart, setSelectedPart] = useState<CourseFile | null>(null);\r\n  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [expandedParts, setExpandedParts] = useState<Set<string>>(new Set());\r\n  const [videoAnalysis, setVideoAnalysis] = useState<VideoAnalysis | null>(null);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [notes, setNotes] = useState<string>('');\r\n  const [showNotes, setShowNotes] = useState(false);\r\n  const [currentPath, setCurrentPath] = useState<string[]>([]);\r\n  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());\r\n  const notesRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  const modulePath = decodeURIComponent(Array.isArray(params.module) ? params.module.join('/') : params.module || '');\r\n\r\n  // Keyboard shortcuts\r\n  useHotkeys('ctrl+/', () => setShowNotes(!showNotes));\r\n  useHotkeys('ctrl+b', () => setSidebarCollapsed(!sidebarCollapsed));\r\n  useHotkeys('escape', () => setSelectedPart(null));\r\n\r\n  useEffect(() => {\r\n    loadCourseStructure();\r\n    loadUserProgress();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (courseStructure && modulePath) {\r\n      findModule();\r\n    }\r\n  }, [courseStructure, modulePath]);\r\n\r\n  useEffect(() => {\r\n    if (selectedPart?.isVideo) {\r\n      loadExistingAnalysis(selectedPart.path);\r\n    } else {\r\n      setVideoAnalysis(null);\r\n    }\r\n  }, [selectedPart]);\r\n\r\n  const loadCourseStructure = async () => {\r\n    try {\r\n      const response = await fetch('/api/course-structure');\r\n      const data = await response.json();\r\n      setCourseStructure(data);\r\n    } catch (error) {\r\n      console.error('Failed to load course structure:', error);\r\n    }\r\n  };\r\n\r\n  const loadUserProgress = async () => {\r\n    const auth = localStorage.getItem('courseAuth');\r\n    if (auth) {\r\n      const { username } = JSON.parse(auth);\r\n      try {\r\n        const response = await fetch(`/api/user-progress?username=${encodeURIComponent(username)}`);\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.success) {\r\n            setUserProgress(data.progress);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load user progress from server:', error);\r\n        // Fallback to localStorage\r\n        const savedProgress = localStorage.getItem(`progress_${username}`);\r\n        if (savedProgress) {\r\n          setUserProgress(JSON.parse(savedProgress));\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  const saveUserProgress = async (updatedProgress: UserProgress) => {\r\n    try {\r\n      const response = await fetch('/api/user-progress', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          username: updatedProgress.username,\r\n          progress: updatedProgress\r\n        })\r\n      });\r\n      \r\n      if (response.ok) {\r\n        // Also save to localStorage as backup\r\n        localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));\r\n      } else {\r\n        console.error('Failed to save progress to server');\r\n        // Fallback to localStorage only\r\n        localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving progress:', error);\r\n      // Fallback to localStorage only\r\n      localStorage.setItem(`progress_${updatedProgress.username}`, JSON.stringify(updatedProgress));\r\n    }\r\n  };\r\n\r\n  const findModule = () => {\r\n    if (!courseStructure) return;\r\n\r\n    console.log('🔍 Finding module for path:', modulePath);\r\n    console.log('📚 Available modules:', courseStructure.modules.map(m => ({ name: m.name, path: m.path, partsCount: m.parts?.length || 0 })));\r\n\r\n    const findModuleRecursive = (modules: CoursePart[]): CoursePart | null => {\r\n      for (const module of modules) {\r\n        console.log('🔎 Checking module:', module.name, 'path:', module.path, 'vs modulePath:', modulePath);\r\n        if (module.path === modulePath) {\r\n          console.log('✅ Found matching module:', module.name, 'with', module.parts?.length || 0, 'parts');\r\n          return module;\r\n        }\r\n        if (module.type === 'module' && module.parts) {\r\n          const found = findModuleRecursive(module.parts as CoursePart[]);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    };\r\n\r\n    const found = findModuleRecursive(courseStructure.modules);\r\n    console.log('🎯 Final result:', found ? `Found ${found.name} with ${found.parts?.length || 0} parts` : 'No module found');\r\n    setCurrentModule(found);\r\n    \r\n    // Auto-select first video if no part is selected\r\n    if (found && !selectedPart) {\r\n      const firstVideo = findFirstVideo(found.parts);\r\n      if (firstVideo) {\r\n        setSelectedPart(firstVideo);\r\n      }\r\n    }\r\n  };\r\n\r\n  const findFirstVideo = (parts: (CourseFile | CoursePart)[]): CourseFile | null => {\r\n    for (const part of parts) {\r\n      if (part.type === 'file' && (part as CourseFile).isVideo) {\r\n        return part as CourseFile;\r\n      }\r\n      if (part.type === 'module') {\r\n        const found = findFirstVideo((part as CoursePart).parts);\r\n        if (found) return found;\r\n      }\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const togglePartExpansion = (partName: string) => {\r\n    const newExpanded = new Set(expandedParts);\r\n    if (newExpanded.has(partName)) {\r\n      newExpanded.delete(partName);\r\n    } else {\r\n      newExpanded.add(partName);\r\n    }\r\n    setExpandedParts(newExpanded);\r\n  };\r\n\r\n  const loadExistingAnalysis = async (videoPath: string) => {\r\n    // Clear previous analysis first\r\n    setVideoAnalysis(null);\r\n    setIsAnalyzing(false);\r\n    \r\n    try {\r\n      const response = await fetch('/api/analyze-video', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ videoPath, checkOnly: true })\r\n      });\r\n      if (response.ok) {\r\n        const analysis = await response.json();\r\n        setVideoAnalysis(analysis);\r\n      } else {\r\n        // No existing analysis found, keep videoAnalysis as null\r\n        console.log('No existing analysis found for this video');\r\n      }\r\n    } catch (error) {\r\n      console.log('No existing analysis found for this video');\r\n      // Keep videoAnalysis as null if there's an error\r\n    }\r\n  };\r\n\r\n  const analyzeVideo = async (videoPath: string) => {\r\n    setIsAnalyzing(true);\r\n    try {\r\n      const response = await fetch('/api/analyze-video', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ videoPath })\r\n      });\r\n      const analysis = await response.json();\r\n      setVideoAnalysis(analysis);\r\n    } catch (error) {\r\n      console.error('Failed to analyze video:', error);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  const saveNote = () => {\r\n    if (!userProgress || !notes.trim()) return;\r\n\r\n    const newNote = {\r\n      id: Date.now().toString(),\r\n      content: notes.trim(),\r\n      timestamp: new Date().toISOString(),\r\n      videoPath: selectedPart?.path,\r\n      videoTime: 0 // TODO: Get current video time\r\n    };\r\n\r\n    const updatedProgress = {\r\n      ...userProgress,\r\n      notes: [...userProgress.notes, newNote]\r\n    };\r\n\r\n    setUserProgress(updatedProgress);\r\n    localStorage.setItem(`progress_${userProgress.username}`, JSON.stringify(updatedProgress));\r\n    setNotes('');\r\n  };\r\n\r\n  const getFileIcon = (file: CourseFile) => {\r\n    if (file.isVideo) return <PlayCircle size={16} className=\"text-red-400\" />;\r\n    if (file.isDocument) return <FileText size={16} className=\"text-blue-400\" />;\r\n    if (file.isImage) return <ImageIcon size={16} className=\"text-green-400\" />;\r\n    return <FileText size={16} className=\"text-gray-400\" />;\r\n  };\r\n\r\n  const getFolderIcon = (path: string, isExpanded: boolean) => {\r\n    return isExpanded ? \r\n      <FolderOpen size={16} className=\"text-yellow-400\" /> : \r\n      <Folder size={16} className=\"text-yellow-400\" />;\r\n  };\r\n\r\n  const toggleFolder = (folderPath: string) => {\r\n    const newExpanded = new Set(expandedFolders);\r\n    if (newExpanded.has(folderPath)) {\r\n      newExpanded.delete(folderPath);\r\n    } else {\r\n      newExpanded.add(folderPath);\r\n    }\r\n    setExpandedFolders(newExpanded);\r\n  };\r\n\r\n  const navigateToFolder = (folderPath: string) => {\r\n    const pathParts = folderPath.split('/');\r\n    setCurrentPath(pathParts);\r\n  };\r\n\r\n  const navigateBack = () => {\r\n    if (currentPath.length > 0) {\r\n      setCurrentPath(currentPath.slice(0, -1));\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    const mb = bytes / (1024 * 1024);\r\n    if (mb > 1000) return `${(mb / 1024).toFixed(1)} GB`;\r\n    return `${mb.toFixed(1)} MB`;\r\n  };\r\n\r\n  const getCurrentParts = () => {\r\n    console.log('📂 getCurrentParts called');\r\n    console.log('📂 currentModule:', currentModule ? `${currentModule.name} with ${currentModule.parts?.length || 0} parts` : 'null');\r\n    console.log('📂 currentPath:', currentPath);\r\n\r\n    if (!currentModule) {\r\n      console.log('❌ No currentModule, returning empty array');\r\n      return [];\r\n    }\r\n\r\n    let currentParts = currentModule.parts;\r\n    console.log('📂 Initial currentParts:', currentParts?.length || 0, 'items');\r\n\r\n    // Navigate through the path to get current folder contents\r\n    for (const pathPart of currentPath) {\r\n      console.log('📂 Navigating to path part:', pathPart);\r\n      const folder = currentParts.find(p => p.name === pathPart && p.type === 'folder');\r\n      if (folder && 'parts' in folder && folder.parts) {\r\n        currentParts = folder.parts;\r\n        console.log('📂 Found folder, now have', currentParts.length, 'parts');\r\n      } else {\r\n        console.log('❌ Folder not found, returning empty array');\r\n        return [];\r\n      }\r\n    }\r\n\r\n    console.log('📂 Final currentParts:', currentParts?.length || 0, 'items');\r\n    return currentParts || [];\r\n  };\r\n\r\n  const filteredParts = getCurrentParts().filter(part => \r\n    part.name.toLowerCase().includes(searchQuery.toLowerCase())\r\n  );\r\n\r\n  const renderPartsList = (parts: (CourseFile | CoursePart)[], level: number = 0) => {\r\n    return parts.map((part, index) => {\r\n      const isFile = part.type === 'file';\r\n      const isFolder = part.type === 'folder';\r\n      const file = part as CourseFile;\r\n      const isSelected = selectedPart?.path === part.path;\r\n      const isExpanded = expandedFolders.has(part.path);\r\n      const indent = level * 20;\r\n      \r\n      return (\r\n        <div key={part.path}>\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ delay: index * 0.05 }}\r\n            className={`p-3 rounded-lg border cursor-pointer transition-all ${\r\n              isSelected \r\n                ? 'bg-blue-500/20 border-blue-400/50' \r\n                : 'bg-white/5 border-white/10 hover:bg-white/10'\r\n            }`}\r\n            style={{ marginLeft: `${indent}px` }}\r\n            onClick={() => {\r\n              if (isFile) {\r\n                setSelectedPart(file);\r\n              } else if (isFolder) {\r\n                toggleFolder(part.path);\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center space-x-3\">\r\n              {isFile && getFileIcon(file)}\r\n              {isFolder && getFolderIcon(part.path, isExpanded)}\r\n              <div className=\"flex-1 min-w-0\">\r\n                {!sidebarCollapsed && (\r\n                  <>\r\n                    <p className=\"text-white text-sm font-medium truncate\">\r\n                      {part.name.replace(/\\.ONEDDL\\.(mp4|docx|pdf|ts|png)$/, '')}\r\n                    </p>\r\n                    {isFile && (\r\n                      <p className=\"text-white/60 text-xs\">\r\n                        {file.fileType} • {formatFileSize(file.size)}\r\n                      </p>\r\n                    )}\r\n                    {isFolder && 'parts' in part && part.parts && (\r\n                      <p className=\"text-white/60 text-xs\">\r\n                        {part.parts.length} items\r\n                      </p>\r\n                    )}\r\n                  </>\r\n                )}\r\n              </div>\r\n              {isFolder && !sidebarCollapsed && (\r\n                <ChevronDown \r\n                  size={14} \r\n                  className={`text-white/60 transition-transform ${\r\n                    isExpanded ? 'rotate-180' : ''\r\n                  }`} \r\n                />\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n          \r\n          {isFolder && isExpanded && 'parts' in part && part.parts && (\r\n            <div className=\"mt-1\">\r\n              {renderPartsList(part.parts, level + 1)}\r\n            </div>\r\n          )}\r\n        </div>\r\n      );\r\n    });\r\n  };\r\n\r\n  const renderFileContent = () => {\r\n    if (!selectedPart) {\r\n      return (\r\n        <div className=\"flex items-center justify-center h-96 text-white/60\">\r\n          <div className=\"text-center\">\r\n            <FileText size={48} className=\"mx-auto mb-4 opacity-50\" />\r\n            <p>Select a file to view its content</p>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (selectedPart.isVideo) {\r\n      return (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-semibold text-white\">{selectedPart.name}</h2>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button \r\n                onClick={() => {\r\n                  const link = document.createElement('a');\r\n                  link.href = `/api/serve-file?path=${encodeURIComponent(selectedPart.path)}`;\r\n                  link.download = selectedPart.name;\r\n                  document.body.appendChild(link);\r\n                  link.click();\r\n                  document.body.removeChild(link);\r\n                }}\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n              >\r\n                <Download size={16} className=\"mr-2\" />\r\n                Download\r\n              </Button>\r\n              {!videoAnalysis && !isAnalyzing && (\r\n                <Button \r\n                  onClick={() => analyzeVideo(selectedPart.path)}\r\n                  variant=\"outline\"\r\n                >\r\n                  Generate AI Analysis\r\n                </Button>\r\n              )}\r\n              {videoAnalysis && (\r\n                <Button \r\n                  onClick={() => analyzeVideo(selectedPart.path)}\r\n                  variant=\"outline\"\r\n                >\r\n                  Regenerate Analysis\r\n                </Button>\r\n              )}\r\n              {isAnalyzing && (\r\n                <div className=\"flex items-center space-x-2 text-white/70\">\r\n                  <div className=\"animate-spin w-4 h-4 border-2 border-white/30 border-t-white rounded-full\"></div>\r\n                  <span className=\"text-sm\">Analyzing with AI...</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          <CustomVideoPlayer\r\n            src={`/api/serve-file?path=${encodeURIComponent(selectedPart.path)}`}\r\n            analysis={videoAnalysis || undefined}\r\n            onTimeUpdate={(time) => {\r\n              // Update user progress\r\n              if (userProgress) {\r\n                const updatedProgress = {\r\n                  ...userProgress,\r\n                  totalWatchTime: userProgress.totalWatchTime + 1,\r\n                  lastAccessed: new Date().toISOString()\r\n                };\r\n                setUserProgress(updatedProgress);\r\n                saveUserProgress(updatedProgress);\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (selectedPart.isDocument) {\r\n      return <DocumentViewer file={selectedPart} />;\r\n    }\r\n\r\n    return (\r\n      <div className=\"glass-morphism p-6 rounded-lg\">\r\n        <h2 className=\"text-xl font-semibold text-white mb-4\">{selectedPart.name}</h2>\r\n        <div className=\"text-white/80\">\r\n          <p>File type: {selectedPart.fileType}</p>\r\n          <p>Size: {formatFileSize(selectedPart.size)}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (!currentModule) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"glass-morphism p-8 text-center\">\r\n          <div className=\"animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4\"></div>\r\n          <p className=\"text-white\">Loading module...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex\">\r\n      {/* Sidebar */}\r\n      <motion.div\r\n        animate={{ width: sidebarCollapsed ? 80 : 400 }}\r\n        transition={{ duration: 0.3 }}\r\n        className=\"bg-black/20 backdrop-blur-md border-r border-white/10 flex flex-col\"\r\n      >\r\n        <div className=\"p-4 border-b border-white/10\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => router.push('/')}\r\n              className=\"text-white hover:bg-white/10\"\r\n            >\r\n              <ArrowLeft size={16} className=\"mr-2\" />\r\n              {!sidebarCollapsed && 'Back to Dashboard'}\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\r\n              className=\"text-white hover:bg-white/10\"\r\n            >\r\n              {sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronDown size={16} />}\r\n            </Button>\r\n          </div>\r\n          \r\n          {!sidebarCollapsed && (\r\n            <>\r\n              <h1 className=\"text-white font-semibold text-lg mb-2\">{currentModule.name}</h1>\r\n              <div className=\"relative\">\r\n                <Search size={16} className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50\" />\r\n                <Input\r\n                  placeholder=\"Search parts...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50\"\r\n                />\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-2\">\r\n          {currentPath.length > 0 && (\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={navigateBack}\r\n              className=\"w-full text-left text-white/70 hover:text-white hover:bg-white/10 mb-4\"\r\n            >\r\n              <ArrowLeft size={16} className=\"mr-2\" />\r\n              Back to {currentPath.length > 1 ? currentPath[currentPath.length - 2] : 'Module'}\r\n            </Button>\r\n          )}\r\n          \r\n          {renderPartsList(filteredParts)}\r\n          \r\n          {filteredParts.length === 0 && (\r\n            <div className=\"text-center py-8 text-white/60\">\r\n              <FileText size={48} className=\"mx-auto mb-4 opacity-50\" />\r\n              <p>No files found in this {currentPath.length > 0 ? 'folder' : 'module'}</p>\r\n              {searchQuery && (\r\n                <p className=\"text-sm mt-2\">Try adjusting your search term</p>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {!sidebarCollapsed && userProgress && (\r\n          <div className=\"p-4 border-t border-white/10\">\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center justify-between text-sm\">\r\n                <span className=\"text-white/70\">Progress</span>\r\n                <span className=\"text-white\">\r\n                  {Math.round((userProgress.watchedVideos.length / (courseStructure?.totalVideos || 1)) * 100)}%\r\n                </span>\r\n              </div>\r\n              <Progress \r\n                value={(userProgress.watchedVideos.length / (courseStructure?.totalVideos || 1)) * 100} \r\n                className=\"h-2\" \r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col\">\r\n        <div className=\"flex-1 p-6\">\r\n          {renderFileContent()}\r\n        </div>\r\n\r\n        {/* Notes Panel */}\r\n        <AnimatePresence>\r\n          {showNotes && (\r\n            <motion.div\r\n              initial={{ height: 0 }}\r\n              animate={{ height: 200 }}\r\n              exit={{ height: 0 }}\r\n              className=\"border-t border-white/10 bg-black/20 backdrop-blur-md overflow-hidden\"\r\n            >\r\n              <div className=\"p-4\">\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                  <h3 className=\"text-white font-medium flex items-center gap-2\">\r\n                    <StickyNote size={16} />\r\n                    Universal Notes\r\n                  </h3>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={saveNote}\r\n                      disabled={!notes.trim()}\r\n                      className=\"bg-blue-500 hover:bg-blue-600\"\r\n                    >\r\n                      Save Note\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"ghost\"\r\n                      onClick={() => setShowNotes(false)}\r\n                      className=\"text-white hover:bg-white/10\"\r\n                    >\r\n                      ×\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                <textarea\r\n                  ref={notesRef}\r\n                  value={notes}\r\n                  onChange={(e) => setNotes(e.target.value)}\r\n                  placeholder=\"Take notes... (Ctrl+/ to toggle)\"\r\n                  className=\"w-full h-24 bg-white/10 border border-white/20 rounded-lg p-3 text-white placeholder:text-white/50 resize-none focus:outline-none focus:border-white/40\"\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n\r\n        {/* Floating Notes Button */}\r\n        {!showNotes && (\r\n          <Button\r\n            onClick={() => setShowNotes(true)}\r\n            className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 rounded-full p-3\"\r\n            title=\"Open Notes (Ctrl+/)\"\r\n          >\r\n            <StickyNote size={20} />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAAA;AAEA;;;AA9BA;;;;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACxE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAE7C,MAAM,aAAa,mBAAmB,MAAM,OAAO,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,OAAO,MAAM,IAAI;IAEhH,qBAAqB;IACrB,CAAA,GAAA,oMAAA,CAAA,aAAU,AAAD,EAAE;iCAAU,IAAM,aAAa,CAAC;;IACzC,CAAA,GAAA,oMAAA,CAAA,aAAU,AAAD,EAAE;iCAAU,IAAM,oBAAoB,CAAC;;IAChD,CAAA,GAAA,oMAAA,CAAA,aAAU,AAAD,EAAE;iCAAU,IAAM,gBAAgB;;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;YACA;QACF;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,mBAAmB,YAAY;gBACjC;YACF;QACF;+BAAG;QAAC;QAAiB;KAAW;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,cAAc,SAAS;gBACzB,qBAAqB,aAAa,IAAI;YACxC,OAAO;gBACL,iBAAiB;YACnB;QACF;+BAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,IAAI,MAAM;YACR,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,KAAK,CAAC;YAChC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,mBAAmB,WAAW;gBAC1F,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,gBAAgB,KAAK,QAAQ;oBAC/B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,2BAA2B;gBAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,UAAU;gBACjE,IAAI,eAAe;oBACjB,gBAAgB,KAAK,KAAK,CAAC;gBAC7B;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,gBAAgB,QAAQ;oBAClC,UAAU;gBACZ;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,sCAAsC;gBACtC,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,gBAAgB,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;YAC9E,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,gCAAgC;gBAChC,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,gBAAgB,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;YAC9E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gCAAgC;YAChC,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,gBAAgB,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;QAC9E;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,iBAAiB;QAEtB,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,MAAM,EAAE,IAAI;gBAAE,YAAY,EAAE,KAAK,EAAE,UAAU;YAAE,CAAC;QAEvI,MAAM,sBAAsB,CAAC;YAC3B,KAAK,MAAM,UAAU,QAAS;gBAC5B,QAAQ,GAAG,CAAC,uBAAuB,OAAO,IAAI,EAAE,SAAS,OAAO,IAAI,EAAE,kBAAkB;gBACxF,IAAI,OAAO,IAAI,KAAK,YAAY;oBAC9B,QAAQ,GAAG,CAAC,4BAA4B,OAAO,IAAI,EAAE,QAAQ,OAAO,KAAK,EAAE,UAAU,GAAG;oBACxF,OAAO;gBACT;gBACA,IAAI,OAAO,IAAI,KAAK,YAAY,OAAO,KAAK,EAAE;oBAC5C,MAAM,QAAQ,oBAAoB,OAAO,KAAK;oBAC9C,IAAI,OAAO,OAAO;gBACpB;YACF;YACA,OAAO;QACT;QAEA,MAAM,QAAQ,oBAAoB,gBAAgB,OAAO;QACzD,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG;QACvG,iBAAiB;QAEjB,iDAAiD;QACjD,IAAI,SAAS,CAAC,cAAc;YAC1B,MAAM,aAAa,eAAe,MAAM,KAAK;YAC7C,IAAI,YAAY;gBACd,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,UAAU,AAAC,KAAoB,OAAO,EAAE;gBACxD,OAAO;YACT;YACA,IAAI,KAAK,IAAI,KAAK,UAAU;gBAC1B,MAAM,QAAQ,eAAe,AAAC,KAAoB,KAAK;gBACvD,IAAI,OAAO,OAAO;YACpB;QACF;QACA,OAAO;IACT;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC7B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,iBAAiB;IACnB;IAEA,MAAM,uBAAuB,OAAO;QAClC,gCAAgC;QAChC,iBAAiB;QACjB,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW,WAAW;gBAAK;YACpD;YACA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,iBAAiB;YACnB,OAAO;gBACL,yDAAyD;gBACzD,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACZ,iDAAiD;QACnD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YACA,MAAM,WAAW,MAAM,SAAS,IAAI;YACpC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,IAAI;QAEpC,MAAM,UAAU;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,MAAM,IAAI;YACnB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,cAAc;YACzB,WAAW,EAAE,+BAA+B;QAC9C;QAEA,MAAM,kBAAkB;YACtB,GAAG,YAAY;YACf,OAAO;mBAAI,aAAa,KAAK;gBAAE;aAAQ;QACzC;QAEA,gBAAgB;QAChB,aAAa,OAAO,CAAC,CAAC,SAAS,EAAE,aAAa,QAAQ,EAAE,EAAE,KAAK,SAAS,CAAC;QACzE,SAAS;IACX;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,OAAO,EAAE,qBAAO,6LAAC,qNAAA,CAAA,aAAU;YAAC,MAAM;YAAI,WAAU;;;;;;QACzD,IAAI,KAAK,UAAU,EAAE,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,MAAM;YAAI,WAAU;;;;;;QAC1D,IAAI,KAAK,OAAO,EAAE,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,MAAM;YAAI,WAAU;;;;;;QACxD,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,MAAM;YAAI,WAAU;;;;;;IACvC;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,OAAO,2BACL,6LAAC,qNAAA,CAAA,aAAU;YAAC,MAAM;YAAI,WAAU;;;;;iCAChC,6LAAC,yMAAA,CAAA,SAAM;YAAC,MAAM;YAAI,WAAU;;;;;;IAChC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,aAAa;YAC/B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,WAAW,KAAK,CAAC;QACnC,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;QACvC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,KAAK,QAAQ,CAAC,OAAO,IAAI;QAC/B,IAAI,KAAK,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QACpD,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;IAC9B;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB,gBAAgB,GAAG,cAAc,IAAI,CAAC,MAAM,EAAE,cAAc,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG;QAC1H,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,IAAI,eAAe,cAAc,KAAK;QACtC,QAAQ,GAAG,CAAC,4BAA4B,cAAc,UAAU,GAAG;QAEnE,2DAA2D;QAC3D,KAAK,MAAM,YAAY,YAAa;YAClC,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,MAAM,SAAS,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,EAAE,IAAI,KAAK;YACxE,IAAI,UAAU,WAAW,UAAU,OAAO,KAAK,EAAE;gBAC/C,eAAe,OAAO,KAAK;gBAC3B,QAAQ,GAAG,CAAC,6BAA6B,aAAa,MAAM,EAAE;YAChE,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF;QAEA,QAAQ,GAAG,CAAC,0BAA0B,cAAc,UAAU,GAAG;QACjE,OAAO,gBAAgB,EAAE;IAC3B;IAEA,MAAM,gBAAgB,kBAAkB,MAAM,CAAC,CAAA,OAC7C,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG1D,MAAM,kBAAkB,CAAC,OAAoC,QAAgB,CAAC;QAC5E,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;YACtB,MAAM,SAAS,KAAK,IAAI,KAAK;YAC7B,MAAM,WAAW,KAAK,IAAI,KAAK;YAC/B,MAAM,OAAO;YACb,MAAM,aAAa,cAAc,SAAS,KAAK,IAAI;YACnD,MAAM,aAAa,gBAAgB,GAAG,CAAC,KAAK,IAAI;YAChD,MAAM,SAAS,QAAQ;YAEvB,qBACE,6LAAC;;kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAK;wBAClC,WAAW,CAAC,oDAAoD,EAC9D,aACI,sCACA,gDACJ;wBACF,OAAO;4BAAE,YAAY,GAAG,OAAO,EAAE,CAAC;wBAAC;wBACnC,SAAS;4BACP,IAAI,QAAQ;gCACV,gBAAgB;4BAClB,OAAO,IAAI,UAAU;gCACnB,aAAa,KAAK,IAAI;4BACxB;wBACF;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,UAAU,YAAY;gCACtB,YAAY,cAAc,KAAK,IAAI,EAAE;8CACtC,6LAAC;oCAAI,WAAU;8CACZ,CAAC,kCACA;;0DACE,6LAAC;gDAAE,WAAU;0DACV,KAAK,IAAI,CAAC,OAAO,CAAC,oCAAoC;;;;;;4CAExD,wBACC,6LAAC;gDAAE,WAAU;;oDACV,KAAK,QAAQ;oDAAC;oDAAI,eAAe,KAAK,IAAI;;;;;;;4CAG9C,YAAY,WAAW,QAAQ,KAAK,KAAK,kBACxC,6LAAC;gDAAE,WAAU;;oDACV,KAAK,KAAK,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;gCAM5B,YAAY,CAAC,kCACZ,6LAAC,uNAAA,CAAA,cAAW;oCACV,MAAM;oCACN,WAAW,CAAC,mCAAmC,EAC7C,aAAa,eAAe,IAC5B;;;;;;;;;;;;;;;;;oBAMT,YAAY,cAAc,WAAW,QAAQ,KAAK,KAAK,kBACtD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,KAAK,KAAK,EAAE,QAAQ;;;;;;;eAtDjC,KAAK,IAAI;;;;;QA2DvB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;YACjB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC9B,6LAAC;sCAAE;;;;;;;;;;;;;;;;;QAIX;QAEA,IAAI,aAAa,OAAO,EAAE;YACxB,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC,aAAa,IAAI;;;;;;0CACnE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,MAAM,OAAO,SAAS,aAAa,CAAC;4CACpC,KAAK,IAAI,GAAG,CAAC,qBAAqB,EAAE,mBAAmB,aAAa,IAAI,GAAG;4CAC3E,KAAK,QAAQ,GAAG,aAAa,IAAI;4CACjC,SAAS,IAAI,CAAC,WAAW,CAAC;4CAC1B,KAAK,KAAK;4CACV,SAAS,IAAI,CAAC,WAAW,CAAC;wCAC5B;wCACA,SAAQ;wCACR,MAAK;;0DAEL,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;oCAGxC,CAAC,iBAAiB,CAAC,6BAClB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,aAAa,aAAa,IAAI;wCAC7C,SAAQ;kDACT;;;;;;oCAIF,+BACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,aAAa,aAAa,IAAI;wCAC7C,SAAQ;kDACT;;;;;;oCAIF,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC,mJAAA,CAAA,UAAiB;wBAChB,KAAK,CAAC,qBAAqB,EAAE,mBAAmB,aAAa,IAAI,GAAG;wBACpE,UAAU,iBAAiB;wBAC3B,cAAc,CAAC;4BACb,uBAAuB;4BACvB,IAAI,cAAc;gCAChB,MAAM,kBAAkB;oCACtB,GAAG,YAAY;oCACf,gBAAgB,aAAa,cAAc,GAAG;oCAC9C,cAAc,IAAI,OAAO,WAAW;gCACtC;gCACA,gBAAgB;gCAChB,iBAAiB;4BACnB;wBACF;;;;;;;;;;;;QAIR;QAEA,IAAI,aAAa,UAAU,EAAE;YAC3B,qBAAO,6LAAC,mJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;QAC/B;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAyC,aAAa,IAAI;;;;;;8BACxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCAAE;gCAAY,aAAa,QAAQ;;;;;;;sCACpC,6LAAC;;gCAAE;gCAAO,eAAe,aAAa,IAAI;;;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO,mBAAmB,KAAK;gBAAI;gBAC9C,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAC9B,CAAC,oBAAoB;;;;;;;kDAExB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,iCAAmB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,MAAM;;;;;iEAAS,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;;;;;;;;;;;;4BAIvE,CAAC,kCACA;;kDACE,6LAAC;wCAAG,WAAU;kDAAyC,cAAc,IAAI;;;;;;kDACzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;kCAOpB,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,MAAM,GAAG,mBACpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;oCAC/B,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,GAAG;;;;;;;4BAI3E,gBAAgB;4BAEhB,cAAc,MAAM,KAAK,mBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,6LAAC;;4CAAE;4CAAwB,YAAY,MAAM,GAAG,IAAI,WAAW;;;;;;;oCAC9D,6BACC,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;oBAMnC,CAAC,oBAAoB,8BACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,AAAC,aAAa,aAAa,CAAC,MAAM,GAAG,CAAC,iBAAiB,eAAe,CAAC,IAAK;gDAAK;;;;;;;;;;;;;8CAGjG,6LAAC,uIAAA,CAAA,WAAQ;oCACP,OAAO,AAAC,aAAa,aAAa,CAAC,MAAM,GAAG,CAAC,iBAAiB,eAAe,CAAC,IAAK;oCACnF,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIH,6LAAC,4LAAA,CAAA,kBAAe;kCACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;4BAAE;4BACrB,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,MAAM;gCAAE,QAAQ;4BAAE;4BAClB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,qNAAA,CAAA,aAAU;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,MAAM,IAAI;wDACrB,WAAU;kEACX;;;;;;kEAGD,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa;wDAC5B,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAKL,6LAAC;wCACC,KAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAQnB,CAAC,2BACA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,qNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GAjnBwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAmBxB,oMAAA,CAAA,aAAU;QACV,oMAAA,CAAA,aAAU;QACV,oMAAA,CAAA,aAAU;;;KAvBY", "debugId": null}}]}