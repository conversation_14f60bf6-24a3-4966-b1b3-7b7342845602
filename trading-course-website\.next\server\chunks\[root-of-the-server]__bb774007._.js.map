{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/app/api/extract-text/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport fs from 'fs';\r\nimport path from 'path';\r\n\r\n// Function to clean extracted text by removing metadata header\r\nfunction cleanExtractedText(content: string): string {\r\n  const lines = content.split('\\n');\r\n\r\n  // Look for the separator line (================================================================================)\r\n  const separatorIndex = lines.findIndex(line => line.includes('================================================================================'));\r\n\r\n  if (separatorIndex !== -1) {\r\n    // Return content after the separator, removing any leading empty lines\r\n    const cleanLines = lines.slice(separatorIndex + 1);\r\n\r\n    // Remove leading empty lines\r\n    while (cleanLines.length > 0 && cleanLines[0].trim() === '') {\r\n      cleanLines.shift();\r\n    }\r\n\r\n    return cleanLines.join('\\n');\r\n  }\r\n\r\n  // If no separator found, return original content (fallback)\r\n  return content;\r\n}\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const { filePath } = await request.json();\r\n    \r\n    if (!filePath) {\r\n      return NextResponse.json({ error: 'File path is required' }, { status: 400 });\r\n    }\r\n\r\n    // First, try to find the extracted text file\r\n    const courseDir = path.join(process.cwd(), '..');\r\n    const extractedTextsDir = path.join(courseDir, '_extracted_texts');\r\n    \r\n    // Convert original file path to extracted text file path\r\n    const originalExtension = path.extname(filePath).toLowerCase();\r\n    const originalBaseName = path.basename(filePath, originalExtension);\r\n    const originalDirName = path.dirname(filePath);\r\n    const extractedTextPath = path.join(extractedTextsDir, originalDirName, `${originalBaseName}.txt`);\r\n    \r\n    // Check if extracted text exists\r\n    if (fs.existsSync(extractedTextPath)) {\r\n      try {\r\n        const extractedText = fs.readFileSync(extractedTextPath, 'utf8');\r\n\r\n        // Remove metadata header from extracted text\r\n        const cleanContent = cleanExtractedText(extractedText);\r\n\r\n        return NextResponse.json({\r\n          success: true,\r\n          content: cleanContent,\r\n          cached: true,\r\n          source: 'extracted'\r\n        });\r\n      } catch (error) {\r\n        console.error('Error reading extracted text:', error);\r\n      }\r\n    }\r\n\r\n    // Fallback: construct original file path\r\n    let fullPath: string;\r\n    \r\n    if (path.isAbsolute(filePath)) {\r\n      fullPath = filePath;\r\n    } else {\r\n      const possiblePaths = [\r\n        path.join(courseDir, filePath),\r\n        path.join(courseDir, 'Photon Trading Zero to Funded 4.0 (2025) @Trading_Tuts', filePath),\r\n        path.join(process.cwd(), filePath),\r\n        filePath\r\n      ];\r\n      \r\n      fullPath = possiblePaths.find(p => {\r\n        try {\r\n          return fs.existsSync(p);\r\n        } catch {\r\n          return false;\r\n        }\r\n      }) || possiblePaths[0];\r\n    }\r\n\r\n    // Security check: ensure the path is within allowed directories\r\n    const publicDir = path.join(process.cwd(), 'public');\r\n    const normalizedPath = path.normalize(fullPath);\r\n    const normalizedCourseDir = path.normalize(courseDir);\r\n    const normalizedPublicDir = path.normalize(publicDir);\r\n    \r\n    const isInCourseDir = normalizedPath.startsWith(normalizedCourseDir);\r\n    const isInPublicDir = normalizedPath.startsWith(normalizedPublicDir);\r\n    \r\n    if (!isInCourseDir && !isInPublicDir) {\r\n      return NextResponse.json({ error: 'Access denied' }, { status: 403 });\r\n    }\r\n\r\n    // If no extracted text found, return fallback message\r\n    const extension = path.extname(filePath).toLowerCase();\r\n    const baseName = path.basename(filePath, extension);\r\n    \r\n    let fallbackContent = '';\r\n    \r\n    switch (extension) {\r\n      case '.pdf':\r\n        fallbackContent = `[PDF Document: ${baseName}]\r\n\r\nThis is a PDF document. Text extraction is available but not yet processed for this file.\r\n\r\nTo view the full content:\r\n1. Download the document using the download button\r\n2. Open it with a PDF viewer\r\n\r\nThe extracted text will be available after processing.`;\r\n        break;\r\n        \r\n      case '.docx':\r\n        fallbackContent = `[DOCX Document: ${baseName}]\r\n\r\nThis is a Microsoft Word document. Text extraction is available but not yet processed for this file.\r\n\r\nTo view the full content:\r\n1. Download the document using the download button\r\n2. Open it with Microsoft Word or a compatible application\r\n\r\nThe extracted text will be available after processing.`;\r\n        break;\r\n        \r\n      case '.txt':\r\n        // For TXT files, try to read directly if no extracted version exists\r\n        if (fs.existsSync(fullPath)) {\r\n          try {\r\n            fallbackContent = fs.readFileSync(fullPath, 'utf8');\r\n          } catch (error) {\r\n            fallbackContent = `Error reading text file: ${(error as Error).message}`;\r\n          }\r\n        } else {\r\n          fallbackContent = `Text file not found: ${filePath}`;\r\n        }\r\n        break;\r\n        \r\n      default:\r\n        fallbackContent = `[${extension.toUpperCase()} Document: ${baseName}]\r\n\r\nThis document type is not supported for text extraction.\r\n\r\nTo view this document:\r\n1. Download the file using the download button\r\n2. Open it with an appropriate application`;\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      content: fallbackContent,\r\n      cached: false,\r\n      source: 'fallback'\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Error extracting text:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to extract text from document' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,+DAA+D;AAC/D,SAAS,mBAAmB,OAAe;IACzC,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAE5B,iHAAiH;IACjH,MAAM,iBAAiB,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;IAE7D,IAAI,mBAAmB,CAAC,GAAG;QACzB,uEAAuE;QACvE,MAAM,aAAa,MAAM,KAAK,CAAC,iBAAiB;QAEhD,6BAA6B;QAC7B,MAAO,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,EAAE,CAAC,IAAI,OAAO,GAAI;YAC3D,WAAW,KAAK;QAClB;QAEA,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,4DAA4D;IAC5D,OAAO;AACT;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAwB,GAAG;gBAAE,QAAQ;YAAI;QAC7E;QAEA,6CAA6C;QAC7C,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC3C,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAE/C,yDAAyD;QACzD,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;QAC5D,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,UAAU;QACjD,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QACrC,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,mBAAmB,iBAAiB,GAAG,iBAAiB,IAAI,CAAC;QAEjG,iCAAiC;QACjC,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,oBAAoB;YACpC,IAAI;gBACF,MAAM,gBAAgB,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,mBAAmB;gBAEzD,6CAA6C;gBAC7C,MAAM,eAAe,mBAAmB;gBAExC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;oBACT,QAAQ;oBACR,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;QAEA,yCAAyC;QACzC,IAAI;QAEJ,IAAI,iGAAA,CAAA,UAAI,CAAC,UAAU,CAAC,WAAW;YAC7B,WAAW;QACb,OAAO;YACL,MAAM,gBAAgB;gBACpB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;gBACrB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW,0DAA0D;gBAC/E,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;gBACzB;aACD;YAED,WAAW,cAAc,IAAI,CAAC,CAAA;gBAC5B,IAAI;oBACF,OAAO,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;gBACvB,EAAE,OAAM;oBACN,OAAO;gBACT;YACF,MAAM,aAAa,CAAC,EAAE;QACxB;QAEA,gEAAgE;QAChE,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC3C,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,SAAS,CAAC;QACtC,MAAM,sBAAsB,iGAAA,CAAA,UAAI,CAAC,SAAS,CAAC;QAC3C,MAAM,sBAAsB,iGAAA,CAAA,UAAI,CAAC,SAAS,CAAC;QAE3C,MAAM,gBAAgB,eAAe,UAAU,CAAC;QAChD,MAAM,gBAAgB,eAAe,UAAU,CAAC;QAEhD,IAAI,CAAC,iBAAiB,CAAC,eAAe;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,sDAAsD;QACtD,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;QACpD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,UAAU;QAEzC,IAAI,kBAAkB;QAEtB,OAAQ;YACN,KAAK;gBACH,kBAAkB,CAAC,eAAe,EAAE,SAAS;;;;;;;;sDAQC,CAAC;gBAC/C;YAEF,KAAK;gBACH,kBAAkB,CAAC,gBAAgB,EAAE,SAAS;;;;;;;;sDAQA,CAAC;gBAC/C;YAEF,KAAK;gBACH,qEAAqE;gBACrE,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;oBAC3B,IAAI;wBACF,kBAAkB,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;oBAC9C,EAAE,OAAO,OAAO;wBACd,kBAAkB,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;oBAC1E;gBACF,OAAO;oBACL,kBAAkB,CAAC,qBAAqB,EAAE,UAAU;gBACtD;gBACA;YAEF;gBACE,kBAAkB,CAAC,CAAC,EAAE,UAAU,WAAW,GAAG,WAAW,EAAE,SAAS;;;;;;0CAMlC,CAAC;QACvC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}