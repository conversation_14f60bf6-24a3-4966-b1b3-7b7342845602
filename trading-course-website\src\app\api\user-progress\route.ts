import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const PROGRESS_DIR = path.join(process.cwd(), 'user-progress');

// Ensure the progress directory exists
function ensureProgressDir() {
  if (!fs.existsSync(PROGRESS_DIR)) {
    fs.mkdirSync(PROGRESS_DIR, { recursive: true });
  }
}

// Get user progress file path
function getUserProgressPath(username: string) {
  return path.join(PROGRESS_DIR, `${username}.json`);
}

// GET - Retrieve user progress or all users
export async function GET(request: NextRequest) {
  try {
    ensureProgressDir();
    
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    const action = searchParams.get('action');

    if (action === 'all') {
      // Return all users' progress for community features
      const userFiles = fs.readdirSync(PROGRESS_DIR).filter(file => file.endsWith('.json'));
      const allUsers: Record<string, any> = {};

      for (const file of userFiles) {
        const userName = path.basename(file, '.json');
        const filePath = path.join(PROGRESS_DIR, file);
        
        try {
          const userProgress = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          allUsers[userName] = userProgress;
        } catch (error) {
          console.error(`Error reading progress for user ${userName}:`, error);
        }
      }

      return NextResponse.json({
        success: true,
        users: allUsers,
        totalUsers: Object.keys(allUsers).length,
        lastUpdated: new Date().toISOString()
      });
    }

    if (!username) {
      return NextResponse.json({ error: 'Username is required' }, { status: 400 });
    }

    const userProgressPath = getUserProgressPath(username);

    if (!fs.existsSync(userProgressPath)) {
      // Return default progress structure for new users
      const defaultProgress = {
        username,
        totalWatchTime: 0,
        completedModules: [],
        currentModule: null,
        watchedVideos: [],
        viewedDocuments: [],
        notes: [],
        analytics: {
          learningStreak: 0,
          sessionsCount: 0,
          averageSessionTime: 0,
          dailyProgress: {},
          weeklyGoal: 0,
          monthlyGoal: 0
        },
        moduleProgress: {},
        lastAccessed: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return NextResponse.json({
        success: true,
        progress: defaultProgress,
        isNew: true
      });
    }

    const userProgress = JSON.parse(fs.readFileSync(userProgressPath, 'utf8'));
    
    return NextResponse.json({
      success: true,
      progress: userProgress,
      isNew: false
    });

  } catch (error) {
    console.error('Error retrieving user progress:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve user progress' },
      { status: 500 }
    );
  }
}

// POST - Save/Update user progress
export async function POST(request: NextRequest) {
  try {
    ensureProgressDir();
    
    const { username, progress } = await request.json();

    if (!username) {
      return NextResponse.json({ error: 'Username is required' }, { status: 400 });
    }

    if (!progress) {
      return NextResponse.json({ error: 'Progress data is required' }, { status: 400 });
    }

    const userProgressPath = getUserProgressPath(username);
    
    // Update timestamps
    const updatedProgress = {
      ...progress,
      username,
      updatedAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString()
    };

    // If this is a new user, set creation timestamp
    if (!fs.existsSync(userProgressPath)) {
      updatedProgress.createdAt = new Date().toISOString();
    }

    // Save to file
    fs.writeFileSync(userProgressPath, JSON.stringify(updatedProgress, null, 2));

    return NextResponse.json({
      success: true,
      message: 'Progress saved successfully',
      progress: updatedProgress
    });

  } catch (error) {
    console.error('Error saving user progress:', error);
    return NextResponse.json(
      { error: 'Failed to save user progress' },
      { status: 500 }
    );
  }
}

// DELETE - Remove user progress (optional)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');

    if (!username) {
      return NextResponse.json({ error: 'Username is required' }, { status: 400 });
    }

    const userProgressPath = getUserProgressPath(username);

    if (fs.existsSync(userProgressPath)) {
      fs.unlinkSync(userProgressPath);
      return NextResponse.json({
        success: true,
        message: 'User progress deleted successfully'
      });
    } else {
      return NextResponse.json({ error: 'User progress not found' }, { status: 404 });
    }

  } catch (error) {
    console.error('Error deleting user progress:', error);
    return NextResponse.json(
      { error: 'Failed to delete user progress' },
      { status: 500 }
    );
  }
}