'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Download, FileText, Eye, Check } from 'lucide-react';
import { CourseFile, UserProgress } from '@/types/course';

interface DocumentViewerProps {
  file: CourseFile;
  userProgress?: UserProgress | null;
  onProgressUpdate?: (progress: UserProgress) => void;
}

export default function DocumentViewer({ file, userProgress, onProgressUpdate }: DocumentViewerProps) {
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [extractedText, setExtractedText] = useState<string>('');
  const [isExtracting, setIsExtracting] = useState(false);

  const loadDocumentContent = async () => {
    // Always try to extract text for better viewing
    setIsExtracting(true);
    setError('');
    
    try {
      const response = await fetch('/api/extract-text', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath: file.path })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setExtractedText(result.content);
          
          // For .txt files, also load the original content
          if (file.extension === '.txt') {
            setIsLoading(true);
            try {
              const fileResponse = await fetch(`/api/serve-file?path=${encodeURIComponent(file.path)}`);
              if (fileResponse.ok) {
                const text = await fileResponse.text();
                setContent(text);
              }
            } catch (err) {
              console.error('Error loading original text file:', err);
            } finally {
              setIsLoading(false);
            }
          }
        } else {
          setError('Failed to extract document content');
        }
      } else {
        setError('Failed to extract document content');
      }
    } catch (err) {
      console.error('Error extracting text:', err);
      setError('Error extracting document content');
    } finally {
      setIsExtracting(false);
    }
  };

  useEffect(() => {
    loadDocumentContent();
  }, [file.path]);

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    if (mb > 1000) return `${(mb / 1024).toFixed(1)} GB`;
    return `${mb.toFixed(1)} MB`;
  };

  const downloadFile = async () => {
    try {
      const response = await fetch(`/api/serve-file?path=${encodeURIComponent(file.path)}`);
      if (!response.ok) {
        throw new Error('Download failed');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Download error:', err);
      setError('Failed to download file');
    }
  };

  const isCompleted = userProgress?.viewedDocuments?.includes(file.path) || false;

  const toggleCompletion = () => {
    if (!userProgress || !onProgressUpdate) return;

    const viewedDocuments = userProgress.viewedDocuments || [];
    const updatedViewedDocuments = isCompleted
      ? viewedDocuments.filter(path => path !== file.path)
      : [...viewedDocuments, file.path];

    const updatedProgress = {
      ...userProgress,
      viewedDocuments: updatedViewedDocuments,
      lastAccessed: new Date().toISOString()
    };

    onProgressUpdate(updatedProgress);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText size={24} className="text-blue-400" />
          <div>
            <h2 className="text-xl font-semibold text-white">{file.name}</h2>
            <p className="text-white/60 text-sm">
              {file.fileType.toUpperCase()} • {formatFileSize(file.size)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {userProgress && onProgressUpdate && (
            <Button
              onClick={toggleCompletion}
              variant={isCompleted ? "default" : "outline"}
              className={`${
                isCompleted
                  ? "bg-green-500 hover:bg-green-600 text-white"
                  : "border-white/20 text-white hover:bg-white/10"
              }`}
            >
              <Check size={16} className="mr-2" />
              {isCompleted ? "Completed" : "Mark Complete"}
            </Button>
          )}
          {file.extension === '.pdf' && (
            <Button
              onClick={() => window.open(`/api/serve-file?path=${encodeURIComponent(file.path)}`, '_blank')}
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
            >
              <Eye size={16} className="mr-2" />
              View PDF
            </Button>
          )}
          <Button
            onClick={downloadFile}
            variant="outline"
            className="border-white/20 text-white hover:bg-white/10"
          >
            <Download size={16} className="mr-2" />
            Download
          </Button>
        </div>
      </div>
      
      <div className="glass-morphism-dark p-6 rounded-lg">
        {(isLoading || isExtracting) && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin w-6 h-6 border-2 border-white/30 border-t-white rounded-full"></div>
            <span className="ml-3 text-white/70">
              {isExtracting ? 'Extracting text...' : 'Loading document...'}
            </span>
          </div>
        )}
        
        {error && (
          <div className="text-center py-8">
            <FileText size={48} className="mx-auto mb-4 text-white/30" />
            <p className="text-white/70 mb-2">Unable to preview this document</p>
            <p className="text-white/50 text-sm">{error}</p>
          </div>
        )}
        
        {extractedText && (
          <div className="text-white/80">
            <h3 className="text-lg font-medium mb-4 text-white">Document Content</h3>
            <div className="text-sm leading-relaxed bg-black/20 p-4 rounded-lg">
              {file.extension === '.txt' && content ? (
                <pre className="whitespace-pre-wrap font-mono">{content}</pre>
              ) : (
                <div className="whitespace-pre-wrap">{extractedText}</div>
              )}
            </div>
          </div>
        )}
        
        {file.extension === '.pdf' && (
          <div className="text-center py-8">
            <FileText size={48} className="mx-auto mb-4 text-blue-400" />
            <p className="text-white/80 mb-2">PDF Document</p>
            <p className="text-white/60 text-sm mb-4">
              Click "View PDF" to open in a new tab or download to view locally.
            </p>
            <div className="flex justify-center space-x-4">
              <Button
                onClick={() => window.open(`/api/serve-file?path=${encodeURIComponent(file.path)}`, '_blank')}
                className="bg-blue-500 hover:bg-blue-600"
              >
                <Eye size={16} className="mr-2" />
                View in Browser
              </Button>
            </div>
          </div>
        )}
        
        {file.extension === '.docx' && (
          <div className="text-center py-8">
            <FileText size={48} className="mx-auto mb-4 text-green-400" />
            <p className="text-white/80 mb-2">Microsoft Word Document</p>
            <p className="text-white/60 text-sm mb-4">
              {file.name} ({formatFileSize(file.size)})
            </p>
            <p className="text-white/60 text-sm mb-4">
              This document contains course materials. Download to view the full content.
            </p>
            <div className="flex justify-center space-x-4">
              <Button
                onClick={downloadFile}
                className="bg-green-500 hover:bg-green-600"
              >
                <Download size={16} className="mr-2" />
                Download Document
              </Button>
            </div>
          </div>
        )}
        
        {!extractedText && !isLoading && !isExtracting && !error && (
          <div className="text-center py-8">
            <FileText size={48} className="mx-auto mb-4 text-white/30" />
            <p className="text-white/70 mb-2">{file.name}</p>
            <p className="text-white/60 text-sm mb-4">
              {file.fileType.toUpperCase()} file ({formatFileSize(file.size)})
            </p>
            <p className="text-white/60 text-sm mb-4">
              This file type cannot be previewed in the browser. Download to view on your computer.
            </p>
            <Button
              onClick={downloadFile}
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
            >
              <Download size={16} className="mr-2" />
              Download File
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}