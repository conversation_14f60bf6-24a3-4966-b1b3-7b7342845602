{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/config/modules.ts"], "sourcesContent": ["export const PREDEFINED_MODULES = [\r\n  {\r\n    id: 1,\r\n    name: \"Start Here\",\r\n    description: \"Welcome to Photon Trading - Your journey begins\",\r\n    folderPattern: \"1. Start Here\",\r\n    color: \"#667eea\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Intro To FX\",\r\n    description: \"Fundamentals of Foreign Exchange Trading\",\r\n    folderPattern: \"2.  Intro To FX\",\r\n    color: \"#764ba2\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"Risk Management\",\r\n    description: \"Essential risk management strategies\",\r\n    folderPattern: \"3. Risk Management\",\r\n    color: \"#f093fb\"\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"Brokers & Charting\",\r\n    description: \"Choosing brokers and setting up charts\",\r\n    folderPattern: \"4.  Brokers & Charting\",\r\n    color: \"#f5576c\"\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"Trading An Edge\",\r\n    description: \"Developing your trading edge\",\r\n    folderPattern: \"5.  Trading An Edge\",\r\n    color: \"#4facfe\"\r\n  },\r\n  {\r\n    id: 6,\r\n    name: \"Technical Analysis\",\r\n    description: \"Advanced technical analysis techniques\",\r\n    folderPattern: \"6. Technical Analysis\",\r\n    color: \"#43e97b\"\r\n  },\r\n  {\r\n    id: 7,\r\n    name: \"PHOTON STRATEGY\",\r\n    description: \"The complete Photon trading strategy\",\r\n    folderPattern: \"7.  PHOTON STRATEGY (2024)\",\r\n    color: \"#38f9d7\"\r\n  },\r\n  {\r\n    id: 8,\r\n    name: \"Trade Plan\",\r\n    description: \"Creating and executing trade plans\",\r\n    folderPattern: \"8.  TRADE PLAN\",\r\n    color: \"#5ee7df\"\r\n  },\r\n  {\r\n    id: 9,\r\n    name: \"Testing\",\r\n    description: \"Backtesting and forward testing strategies\",\r\n    folderPattern: \"9.  Testing\",\r\n    color: \"#66a6ff\"\r\n  },\r\n  {\r\n    id: 10,\r\n    name: \"Journaling\",\r\n    description: \"Trading journal and performance tracking\",\r\n    folderPattern: \"10.   Journaling\",\r\n    color: \"#89f7fe\"\r\n  },\r\n  {\r\n    id: 11,\r\n    name: \"Moving Forward\",\r\n    description: \"Advanced concepts and next steps\",\r\n    folderPattern: \"11.  Moving Forward\",\r\n    color: \"#b721ff\"\r\n  },\r\n  {\r\n    id: 12,\r\n    name: \"Trading Psychology\",\r\n    description: \"Mastering the mental game of trading\",\r\n    folderPattern: \"12.  Trading Psychology\",\r\n    color: \"#21d4fd\"\r\n  },\r\n  {\r\n    id: 13,\r\n    name: \"Archive\",\r\n    description: \"Additional resources and advanced content\",\r\n    folderPattern: \"13. Archive\",\r\n    color: \"#667eea\"\r\n  }\r\n];"], "names": [], "mappings": ";;;AAAO,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,OAAO;IACT;CACD", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Trading/Photon%20Trading%20Zero%20to%20Funded%204.0%20%282025%29%20%40Trading_Tuts/trading-course-website/src/app/api/scan-course/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport fs from 'fs';\r\nimport path from 'path';\r\nimport { PREDEFINED_MODULES } from '@/config/modules';\r\n\r\ninterface CourseFile {\r\n  name: string;\r\n  path: string;\r\n  type: 'file' | 'folder';\r\n  size: number;\r\n  extension: string;\r\n  isVideo: boolean;\r\n  isDocument: boolean;\r\n  isImage: boolean;\r\n  fileType: string;\r\n}\r\n\r\ninterface CoursePart {\r\n  name: string;\r\n  path: string;\r\n  type: 'file' | 'folder' | 'module';\r\n  parts?: CoursePart[];\r\n  files?: CourseFile[];\r\n  size?: number;\r\n  extension?: string;\r\n  isVideo?: boolean;\r\n  isDocument?: boolean;\r\n  isImage?: boolean;\r\n  fileType?: string;\r\n}\r\n\r\nconst videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v'];\r\nconst documentExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'];\r\nconst imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'];\r\n\r\nfunction getFileType(extension: string): { isVideo: boolean; isDocument: boolean; isImage: boolean; fileType: string } {\r\n  const ext = extension.toLowerCase();\r\n  return {\r\n    isVideo: videoExtensions.includes(ext),\r\n    isDocument: documentExtensions.includes(ext),\r\n    isImage: imageExtensions.includes(ext),\r\n    fileType: ext.substring(1) || 'unknown'\r\n  };\r\n}\r\n\r\nfunction scanDirectory(dirPath: string, basePath: string = ''): CoursePart[] {\r\n  try {\r\n    const items = fs.readdirSync(dirPath);\r\n    const parts: CoursePart[] = [];\r\n\r\n    for (const item of items) {\r\n      // Skip hidden files, system files, and extracted text directories\r\n      if (item.startsWith('.') || \r\n          item === 'Thumbs.db' || \r\n          item === 'desktop.ini' || \r\n          item === '_extracted_texts' ||\r\n          item.startsWith('_extracted_')) {\r\n        continue;\r\n      }\r\n\r\n      const fullPath = path.join(dirPath, item);\r\n      const relativePath = path.posix.join(basePath, item);\r\n      const stats = fs.statSync(fullPath);\r\n\r\n      if (stats.isDirectory()) {\r\n        // This is a folder, scan it recursively\r\n        const subParts = scanDirectory(fullPath, relativePath);\r\n        parts.push({\r\n          name: item,\r\n          path: relativePath,\r\n          type: 'folder',\r\n          parts: subParts\r\n        });\r\n      } else {\r\n        // This is a file\r\n        const extension = path.extname(item);\r\n        const { isVideo, isDocument, isImage, fileType } = getFileType(extension);\r\n        \r\n        parts.push({\r\n          name: item,\r\n          path: relativePath,\r\n          type: 'file',\r\n          size: stats.size,\r\n          extension,\r\n          isVideo,\r\n          isDocument,\r\n          isImage,\r\n          fileType\r\n        });\r\n      }\r\n    }\r\n\r\n    return parts.sort((a, b) => {\r\n      // Sort folders first, then files\r\n      if (a.type === 'folder' && b.type === 'file') return -1;\r\n      if (a.type === 'file' && b.type === 'folder') return 1;\r\n      return a.name.localeCompare(b.name);\r\n    });\r\n  } catch (error) {\r\n    console.error(`Error scanning directory ${dirPath}:`, error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function GET() {\r\n  try {\r\n    const courseDirectory = path.join(process.cwd(), '..');\r\n    \r\n    if (!fs.existsSync(courseDirectory)) {\r\n      return NextResponse.json({ error: 'Course directory not found' }, { status: 404 });\r\n    }\r\n\r\n    // Scan for modules based on predefined patterns\r\n    const modules = [];\r\n    let totalFiles = 0;\r\n    let totalVideos = 0;\r\n    let totalDocuments = 0;\r\n\r\n    for (const predefinedModule of PREDEFINED_MODULES) {\r\n      const modulePath = path.join(courseDirectory, predefinedModule.folderPattern);\r\n      \r\n      if (fs.existsSync(modulePath)) {\r\n        const parts = scanDirectory(modulePath, predefinedModule.folderPattern);\r\n        \r\n        // Count files recursively\r\n        const countFiles = (partsList: CoursePart[]): void => {\r\n          for (const part of partsList) {\r\n            if (part.type === 'file') {\r\n              totalFiles++;\r\n              if (part.isVideo) totalVideos++;\r\n              if (part.isDocument) totalDocuments++;\r\n            } else if (part.parts) {\r\n              countFiles(part.parts);\r\n            }\r\n          }\r\n        };\r\n        \r\n        countFiles(parts);\r\n\r\n        modules.push({\r\n          type: \"module\",\r\n          name: predefinedModule.name,\r\n          path: predefinedModule.folderPattern,\r\n          parts,\r\n          id: predefinedModule.id,\r\n          description: predefinedModule.description,\r\n          color: predefinedModule.color\r\n        });\r\n      } else {\r\n        // Module folder doesn't exist, create empty structure\r\n        modules.push({\r\n          type: \"module\",\r\n          name: predefinedModule.name,\r\n          path: predefinedModule.folderPattern,\r\n          parts: [],\r\n          id: predefinedModule.id,\r\n          description: predefinedModule.description,\r\n          color: predefinedModule.color\r\n        });\r\n      }\r\n    }\r\n\r\n    const courseStructure = {\r\n      title: \"Photon Trading Zero to Funded 4.0 (2025)\",\r\n      modules: modules.sort((a, b) => a.id - b.id),\r\n      totalModules: PREDEFINED_MODULES.length,\r\n      totalFiles,\r\n      totalVideos,\r\n      totalDocuments,\r\n      estimatedDuration: Math.round(totalVideos * 30), // Estimate 30 min per video\r\n      lastScanned: new Date().toISOString()\r\n    };\r\n\r\n    // Save the scanned structure for future use\r\n    const outputPath = path.join(process.cwd(), 'course-structure.json');\r\n    fs.writeFileSync(outputPath, JSON.stringify(courseStructure, null, 2));\r\n\r\n    return NextResponse.json(courseStructure);\r\n  } catch (error) {\r\n    console.error('Error scanning course structure:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to scan course structure' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AA4BA,MAAM,kBAAkB;IAAC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;CAAO;AAChF,MAAM,qBAAqB;IAAC;IAAQ;IAAQ;IAAS;IAAQ;IAAQ;CAAO;AAC5E,MAAM,kBAAkB;IAAC;IAAQ;IAAS;IAAQ;IAAQ;IAAQ;IAAQ;CAAQ;AAElF,SAAS,YAAY,SAAiB;IACpC,MAAM,MAAM,UAAU,WAAW;IACjC,OAAO;QACL,SAAS,gBAAgB,QAAQ,CAAC;QAClC,YAAY,mBAAmB,QAAQ,CAAC;QACxC,SAAS,gBAAgB,QAAQ,CAAC;QAClC,UAAU,IAAI,SAAS,CAAC,MAAM;IAChC;AACF;AAEA,SAAS,cAAc,OAAe,EAAE,WAAmB,EAAE;IAC3D,IAAI;QACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;QAC7B,MAAM,QAAsB,EAAE;QAE9B,KAAK,MAAM,QAAQ,MAAO;YACxB,kEAAkE;YAClE,IAAI,KAAK,UAAU,CAAC,QAChB,SAAS,eACT,SAAS,iBACT,SAAS,sBACT,KAAK,UAAU,CAAC,gBAAgB;gBAClC;YACF;YAEA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;YACpC,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU;YAC/C,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;YAE1B,IAAI,MAAM,WAAW,IAAI;gBACvB,wCAAwC;gBACxC,MAAM,WAAW,cAAc,UAAU;gBACzC,MAAM,IAAI,CAAC;oBACT,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,OAAO;gBACT;YACF,OAAO;gBACL,iBAAiB;gBACjB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAC/B,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,YAAY;gBAE/D,MAAM,IAAI,CAAC;oBACT,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,MAAM,MAAM,IAAI;oBAChB;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG;YACpB,iCAAiC;YACjC,IAAI,EAAE,IAAI,KAAK,YAAY,EAAE,IAAI,KAAK,QAAQ,OAAO,CAAC;YACtD,IAAI,EAAE,IAAI,KAAK,UAAU,EAAE,IAAI,KAAK,UAAU,OAAO;YACrD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC,EAAE;QACtD,OAAO,EAAE;IACX;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAEjD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,kBAAkB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6B,GAAG;gBAAE,QAAQ;YAAI;QAClF;QAEA,gDAAgD;QAChD,MAAM,UAAU,EAAE;QAClB,IAAI,aAAa;QACjB,IAAI,cAAc;QAClB,IAAI,iBAAiB;QAErB,KAAK,MAAM,oBAAoB,0HAAA,CAAA,qBAAkB,CAAE;YACjD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,iBAAiB,iBAAiB,aAAa;YAE5E,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;gBAC7B,MAAM,QAAQ,cAAc,YAAY,iBAAiB,aAAa;gBAEtE,0BAA0B;gBAC1B,MAAM,aAAa,CAAC;oBAClB,KAAK,MAAM,QAAQ,UAAW;wBAC5B,IAAI,KAAK,IAAI,KAAK,QAAQ;4BACxB;4BACA,IAAI,KAAK,OAAO,EAAE;4BAClB,IAAI,KAAK,UAAU,EAAE;wBACvB,OAAO,IAAI,KAAK,KAAK,EAAE;4BACrB,WAAW,KAAK,KAAK;wBACvB;oBACF;gBACF;gBAEA,WAAW;gBAEX,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,MAAM,iBAAiB,IAAI;oBAC3B,MAAM,iBAAiB,aAAa;oBACpC;oBACA,IAAI,iBAAiB,EAAE;oBACvB,aAAa,iBAAiB,WAAW;oBACzC,OAAO,iBAAiB,KAAK;gBAC/B;YACF,OAAO;gBACL,sDAAsD;gBACtD,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,MAAM,iBAAiB,IAAI;oBAC3B,MAAM,iBAAiB,aAAa;oBACpC,OAAO,EAAE;oBACT,IAAI,iBAAiB,EAAE;oBACvB,aAAa,iBAAiB,WAAW;oBACzC,OAAO,iBAAiB,KAAK;gBAC/B;YACF;QACF;QAEA,MAAM,kBAAkB;YACtB,OAAO;YACP,SAAS,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,EAAE,GAAG,EAAE,EAAE;YAC3C,cAAc,0HAAA,CAAA,qBAAkB,CAAC,MAAM;YACvC;YACA;YACA;YACA,mBAAmB,KAAK,KAAK,CAAC,cAAc;YAC5C,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,4CAA4C;QAC5C,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC5C,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,iBAAiB,MAAM;QAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}