import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { filePath } = await request.json();
    
    if (!filePath) {
      return NextResponse.json({ error: 'File path is required' }, { status: 400 });
    }

    // First, try to find the extracted text file
    const courseDir = path.join(process.cwd(), '..');
    const extractedTextsDir = path.join(courseDir, '_extracted_texts');
    
    // Convert original file path to extracted text file path
    const originalExtension = path.extname(filePath).toLowerCase();
    const originalBaseName = path.basename(filePath, originalExtension);
    const originalDirName = path.dirname(filePath);
    const extractedTextPath = path.join(extractedTextsDir, originalDirName, `${originalBaseName}.txt`);
    
    // Check if extracted text exists
    if (fs.existsSync(extractedTextPath)) {
      try {
        const extractedText = fs.readFileSync(extractedTextPath, 'utf8');
        return NextResponse.json({
          success: true,
          content: extractedText,
          cached: true,
          source: 'extracted'
        });
      } catch (error) {
        console.error('Error reading extracted text:', error);
      }
    }

    // Fallback: construct original file path
    let fullPath: string;
    
    if (path.isAbsolute(filePath)) {
      fullPath = filePath;
    } else {
      const possiblePaths = [
        path.join(courseDir, filePath),
        path.join(courseDir, 'Photon Trading Zero to Funded 4.0 (2025) @Trading_Tuts', filePath),
        path.join(process.cwd(), filePath),
        filePath
      ];
      
      fullPath = possiblePaths.find(p => {
        try {
          return fs.existsSync(p);
        } catch {
          return false;
        }
      }) || possiblePaths[0];
    }

    // Security check: ensure the path is within allowed directories
    const publicDir = path.join(process.cwd(), 'public');
    const normalizedPath = path.normalize(fullPath);
    const normalizedCourseDir = path.normalize(courseDir);
    const normalizedPublicDir = path.normalize(publicDir);
    
    const isInCourseDir = normalizedPath.startsWith(normalizedCourseDir);
    const isInPublicDir = normalizedPath.startsWith(normalizedPublicDir);
    
    if (!isInCourseDir && !isInPublicDir) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // If no extracted text found, return fallback message
    const extension = path.extname(filePath).toLowerCase();
    const baseName = path.basename(filePath, extension);
    
    let fallbackContent = '';
    
    switch (extension) {
      case '.pdf':
        fallbackContent = `[PDF Document: ${baseName}]

This is a PDF document. Text extraction is available but not yet processed for this file.

To view the full content:
1. Download the document using the download button
2. Open it with a PDF viewer

The extracted text will be available after processing.`;
        break;
        
      case '.docx':
        fallbackContent = `[DOCX Document: ${baseName}]

This is a Microsoft Word document. Text extraction is available but not yet processed for this file.

To view the full content:
1. Download the document using the download button
2. Open it with Microsoft Word or a compatible application

The extracted text will be available after processing.`;
        break;
        
      case '.txt':
        // For TXT files, try to read directly if no extracted version exists
        if (fs.existsSync(fullPath)) {
          try {
            fallbackContent = fs.readFileSync(fullPath, 'utf8');
          } catch (error) {
            fallbackContent = `Error reading text file: ${(error as Error).message}`;
          }
        } else {
          fallbackContent = `Text file not found: ${filePath}`;
        }
        break;
        
      default:
        fallbackContent = `[${extension.toUpperCase()} Document: ${baseName}]

This document type is not supported for text extraction.

To view this document:
1. Download the file using the download button
2. Open it with an appropriate application`;
    }

    return NextResponse.json({
      success: true,
      content: fallbackContent,
      cached: false,
      source: 'fallback'
    });

  } catch (error) {
    console.error('Error extracting text:', error);
    return NextResponse.json(
      { error: 'Failed to extract text from document' },
      { status: 500 }
    );
  }
}