import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const courseDirectory = path.join(process.cwd(), '..');
    
    const debugInfo = {
      workingDirectory: process.cwd(),
      courseDirectory,
      courseDirectoryExists: fs.existsSync(courseDirectory),
      files: [] as any[]
    };

    if (fs.existsSync(courseDirectory)) {
      // Recursively scan for video files
      const scanForVideos = (dir: string, prefix: string = ''): any[] => {
        const results: any[] = [];
        try {
          const items = fs.readdirSync(dir);
          
          for (const item of items) {
            if (item.startsWith('.')) continue; // Skip hidden files
            
            const itemPath = path.join(dir, item);
            const relativePath = path.join(prefix, item);
            const stats = fs.statSync(itemPath);
            
            if (stats.isDirectory()) {
              // Recursively scan directories
              results.push({
                name: item,
                type: 'directory',
                path: relativePath,
                fullPath: itemPath
              });
              
              // Don't go too deep to avoid infinite loops
              if (prefix.split(path.sep).length < 3) {
                results.push(...scanForVideos(itemPath, relativePath));
              }
            } else {
              const ext = path.extname(item).toLowerCase();
              const isVideo = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v'].includes(ext);
              
              if (isVideo || ext === '.txt' || ext === '.pdf' || ext === '.docx') {
                results.push({
                  name: item,
                  type: 'file',
                  extension: ext,
                  isVideo,
                  path: relativePath,
                  fullPath: itemPath,
                  size: stats.size,
                  exists: true
                });
              }
            }
          }
        } catch (error) {
          results.push({
            error: `Cannot read directory: ${dir}`,
            errorMessage: (error as Error).message
          });
        }
        
        return results;
      };
      
      debugInfo.files = scanForVideos(courseDirectory).slice(0, 100); // Limit to first 100 items
    }

    return NextResponse.json(debugInfo);
    
  } catch (error) {
    console.error('Debug files error:', error);
    return NextResponse.json({
      error: 'Failed to debug files',
      message: (error as Error).message,
      workingDirectory: process.cwd()
    }, { status: 500 });
  }
}