{"name": "trading-course-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/genai": "^1.8.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "fs-extra": "^11.3.0", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "mime": "^4.0.7", "next": "15.3.5", "next-themes": "^0.4.6", "office-text-extractor": "^3.0.3", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hotkeys-hook": "^5.1.0", "recharts": "^3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}