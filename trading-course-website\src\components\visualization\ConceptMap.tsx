'use client';

import { useEffect, useRef } from 'react';
import { VideoAnalysis } from '@/types/course';

interface ConceptMapProps {
  conceptMap: VideoAnalysis['concept_map'];
}

export default function ConceptMap({ conceptMap }: ConceptMapProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!conceptMap || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth * window.devicePixelRatio;
    canvas.height = canvas.offsetHeight * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = canvas.offsetWidth;
    const height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Node positioning
    const nodes = conceptMap.nodes.map((node, index) => ({
      name: node,
      x: (Math.cos((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (width / 2 - 80) + 80,
      y: (Math.sin((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (height / 2 - 40) + 40,
      radius: 30 + node.length * 2
    }));

    // Draw edges
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 2;
    conceptMap.edges.forEach(edge => {
      const fromNode = nodes.find(n => n.name === edge.from);
      const toNode = nodes.find(n => n.name === edge.to);
      
      if (fromNode && toNode) {
        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);
        ctx.stroke();

        // Draw arrow
        const angle = Math.atan2(toNode.y - fromNode.y, toNode.x - fromNode.x);
        const arrowLength = 10;
        ctx.beginPath();
        ctx.moveTo(
          toNode.x - toNode.radius * Math.cos(angle),
          toNode.y - toNode.radius * Math.sin(angle)
        );
        ctx.lineTo(
          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle - 0.5),
          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle - 0.5)
        );
        ctx.moveTo(
          toNode.x - toNode.radius * Math.cos(angle),
          toNode.y - toNode.radius * Math.sin(angle)
        );
        ctx.lineTo(
          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle + 0.5),
          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle + 0.5)
        );
        ctx.stroke();

        // Draw relation label
        const midX = (fromNode.x + toNode.x) / 2;
        const midY = (fromNode.y + toNode.y) / 2;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = '10px "Fira Code"';
        ctx.textAlign = 'center';
        ctx.fillText(edge.relation, midX, midY - 5);
      }
    });

    // Draw nodes
    nodes.forEach((node, index) => {
      // Node background
      const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, node.radius);
      gradient.addColorStop(0, `hsla(${200 + index * 30}, 70%, 60%, 0.8)`);
      gradient.addColorStop(1, `hsla(${200 + index * 30}, 70%, 40%, 0.6)`);
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
      ctx.fill();

      // Node border
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Node text
      ctx.fillStyle = '#ffffff';
      ctx.font = '12px "Fira Code"';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Wrap text for long nodes
      const words = node.name.split(' ');
      if (words.length > 2) {
        const line1 = words.slice(0, Math.ceil(words.length / 2)).join(' ');
        const line2 = words.slice(Math.ceil(words.length / 2)).join(' ');
        ctx.fillText(line1, node.x, node.y - 6);
        ctx.fillText(line2, node.x, node.y + 6);
      } else {
        ctx.fillText(node.name, node.x, node.y);
      }
    });

  }, [conceptMap]);

  if (!conceptMap || conceptMap.nodes.length === 0) {
    return (
      <div className="text-center py-8 text-white/60">
        <p>No concept map available</p>
      </div>
    );
  }

  return (
    <div className="w-full h-64 relative">
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ width: '100%', height: '100%' }}
      />
      <div className="absolute bottom-2 left-2 text-white/60 text-xs">
        Interactive Concept Map
      </div>
    </div>
  );
}