'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { VideoAnalysis } from '@/types/course';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, RotateCcw, Maximize2 } from 'lucide-react';

interface ConceptMapProps {
  conceptMap: VideoAnalysis['concept_map'];
  onOpenDialog?: () => void;
}

export default function ConceptMap({ conceptMap, onOpenDialog }: ConceptMapProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [scale, setScale] = useState(1);
  const [panX, setPanX] = useState(0);
  const [panY, setPanY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });

  const drawConceptMap = useCallback(() => {
    if (!conceptMap || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth * window.devicePixelRatio;
    canvas.height = canvas.offsetHeight * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = canvas.offsetWidth;
    const height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Apply transformations
    ctx.save();
    ctx.translate(panX, panY);
    ctx.scale(scale, scale);

    // Node positioning
    const nodes = conceptMap.nodes.map((node, index) => ({
      name: node,
      x: (Math.cos((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (width / 2 - 80) + 80,
      y: (Math.sin((index * 2 * Math.PI) / conceptMap.nodes.length) + 1) * (height / 2 - 40) + 40,
      radius: 30 + node.length * 2
    }));

    // Draw edges
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 2;
    conceptMap.edges.forEach(edge => {
      const fromNode = nodes.find(n => n.name === edge.from);
      const toNode = nodes.find(n => n.name === edge.to);
      
      if (fromNode && toNode) {
        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);
        ctx.stroke();

        // Draw arrow
        const angle = Math.atan2(toNode.y - fromNode.y, toNode.x - fromNode.x);
        const arrowLength = 10;
        ctx.beginPath();
        ctx.moveTo(
          toNode.x - toNode.radius * Math.cos(angle),
          toNode.y - toNode.radius * Math.sin(angle)
        );
        ctx.lineTo(
          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle - 0.5),
          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle - 0.5)
        );
        ctx.moveTo(
          toNode.x - toNode.radius * Math.cos(angle),
          toNode.y - toNode.radius * Math.sin(angle)
        );
        ctx.lineTo(
          toNode.x - toNode.radius * Math.cos(angle) - arrowLength * Math.cos(angle + 0.5),
          toNode.y - toNode.radius * Math.sin(angle) - arrowLength * Math.sin(angle + 0.5)
        );
        ctx.stroke();

        // Draw relation label
        const midX = (fromNode.x + toNode.x) / 2;
        const midY = (fromNode.y + toNode.y) / 2;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = '10px "Fira Code"';
        ctx.textAlign = 'center';
        ctx.fillText(edge.relation, midX, midY - 5);
      }
    });

    // Draw nodes
    nodes.forEach((node, index) => {
      // Node background
      const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, node.radius);
      gradient.addColorStop(0, `hsla(${200 + index * 30}, 70%, 60%, 0.8)`);
      gradient.addColorStop(1, `hsla(${200 + index * 30}, 70%, 40%, 0.6)`);
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
      ctx.fill();

      // Node border
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Node text
      ctx.fillStyle = '#ffffff';
      ctx.font = '12px "Fira Code"';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Wrap text for long nodes
      const words = node.name.split(' ');
      if (words.length > 2) {
        const line1 = words.slice(0, Math.ceil(words.length / 2)).join(' ');
        const line2 = words.slice(Math.ceil(words.length / 2)).join(' ');
        ctx.fillText(line1, node.x, node.y - 6);
        ctx.fillText(line2, node.x, node.y + 6);
      } else {
        ctx.fillText(node.name, node.x, node.y);
      }
    });

    // Restore canvas state
    ctx.restore();
  }, [conceptMap, scale, panX, panY]);

  useEffect(() => {
    drawConceptMap();
  }, [drawConceptMap]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const deltaX = e.clientX - lastMousePos.x;
    const deltaY = e.clientY - lastMousePos.y;

    setPanX(prev => prev + deltaX);
    setPanY(prev => prev + deltaY);

    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    setScale(prev => Math.max(0.1, Math.min(3, prev * zoomFactor)));
  };

  const resetView = () => {
    setScale(1);
    setPanX(0);
    setPanY(0);
  };

  const zoomIn = () => {
    setScale(prev => Math.min(3, prev * 1.2));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.1, prev * 0.8));
  };

  if (!conceptMap || conceptMap.nodes.length === 0) {
    return (
      <div className="text-center py-8 text-white/60">
        <p>No concept map available</p>
      </div>
    );
  }

  return (
    <div className="w-full h-64 bg-gray-900 rounded-lg border border-gray-700 relative">
      {/* Control buttons */}
      <div className="absolute top-2 right-2 z-10 flex gap-1">
        <Button
          size="sm"
          variant="outline"
          onClick={zoomIn}
          className="bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700"
        >
          <ZoomIn size={14} />
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={zoomOut}
          className="bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700"
        >
          <ZoomOut size={14} />
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={resetView}
          className="bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700"
        >
          <RotateCcw size={14} />
        </Button>
        {onOpenDialog && (
          <Button
            size="sm"
            variant="outline"
            onClick={onOpenDialog}
            className="bg-gray-800/80 border-gray-600 text-white hover:bg-gray-700"
          >
            <Maximize2 size={14} />
          </Button>
        )}
      </div>

      <canvas
        ref={canvasRef}
        className="w-full h-full rounded-lg"
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      />

      {/* Scale indicator */}
      <div className="absolute bottom-2 left-2 bg-gray-800/80 text-white text-xs px-2 py-1 rounded">
        {Math.round(scale * 100)}%
      </div>
    </div>
  );
}