Extracted from: 3. LC-1 - When To Use It (Theory).ONEDDL.pdf
Original file: 7.  PHOTON STRATEGY (2024)/2. LC-1/3. LC-1 - When To Use It (Theory).ONEDDL.pdf
Extraction date: 2025-07-06T09:52:18.036Z
File type: PDF
================================================================================

LC-1 - When To Use It (Theory)
LC-1
WHEN TO USE LC-1
You should approach 80% of scenarios only waiting for LC-2A. 
Think of LC-1 as a 'bonus' entry model that is ideally used in specific circumstances:
•  High-probability (& higher end of medium) scenarios
•  'Tight/small' MTF ranges (you can see LTF LID and the LTF entry on your screen at the 
same time without zooming out) 
•  Start of Phase A / C (Pro MTF Internal !) 
•  Phase B if it's a tight range and from the extreme MTF POI (where we expect an MTF 
Internal reversal) 
•  Scenarios when you would consider taking an MTF risk entry
•  High probability MTF POI (i.e unmitigated extreme / sweep flip / LID / S&D chain / well-
priced in a tight range -> high conviction that the POI will hold if the trend is to continue)
•  Trading in line with the immediate HTF directional bias / POI
 
HIGH PROBABILITY MTF NARRATIVE + POIs

https://s3.tradingview.com/snapshots/t/twLVaBfZ.png 
 
ADDITIONAL RULES YOU MAY WANT TO TEST
This is a more aggressive entry model than LC-2A so you need experience + discipline to 
know when to execute it. 
The following are some potential ideas you may want to test to help make your rules more 
restrictive to limit the number of potentially valid trades:
•  Must be MTF LID 
•  LID must be within the MTF POI (or MTF range POI)
The easiest way to test these is to add them as variables in your backtesting collection.
Then you can filter the results to see how each rule affects the overall statistics.