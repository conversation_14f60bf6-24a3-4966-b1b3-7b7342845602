Extracted from: 1. LC-1 - LID Sweep (Theory).ONEDDL.pdf
Original file: 7.  PHOTON STRATEGY (2024)/2. LC-1/1. LC-1 - LID Sweep (Theory).ONEDDL.pdf
Extraction date: 2025-07-06T09:52:17.998Z
File type: PDF
================================================================================

LC-1 - LID Sweep (Theory)
Entry Model - LC-1
https://s3.tradingview.com/snapshots/u/UxpAPQsG.png 
 
This is a very aggressive entry model so you must only use it in very specific scenarios which 
we will cover in the following lessons.
  
PBID = Pullback Inducement -> available strong LQ built in the pullback (right) [LC-2A]
 
LID = Leg Inducement -> generates available strong LQ built in the MTF structural leg (left) [LC-
1]
We use the LTF structural highs/lows in the MTF leg to the left as strong liquidation points for 
the MTF POI. 
These can be LTF structural H/Ls (LTF LID) but must be in the MTF leg. 
 
LC-1 uses LTF LID -> strong liquidation of LTF structural H/Ls in the MTF leg (left)

LC-2A uses LTF PBID -> strong liquidation [fake break] in the pullback (right) 
 
LC-1 EXECUTION
1) MTF POI mitigation
2) Has LTF LID been liquidated in the MTF leg to the left? 
3) Is the liquidation still valid (PBL / Early Entry)? 
4) Enter on liquidation candle (LC) 
 
WHEN TO USE LC-1
You should approach 80% of scenarios only waiting for LC-2A. 
Think of LC-1 as a 'bonus' entry model that is ideally used in specific circumstances:
•  High-probability (& higher end of medium) scenarios
•  'Tight/small' MTF ranges (you can see LTF LID and the LTF entry on your screen at the 
same time without zooming out) 
•  Start of Phase A / C (Pro MTF Internal !) 
•  Phase B if it's a tight range and from a high probability MTF POI (where we expect an 
MTF Internal reversal) 
•  Scenarios when you would consider taking an MTF risk entry
•  High probability MTF POI (i.e unmitigated extreme / sweep flip / LID / S&D chain / well-
priced in a tight range -> high conviction that the POI will hold if the trend is to continue)
•  Trading in line with the immediate HTF directional bias / POI
 
ADDITIONAL RULES YOU MAY WANT TO TEST
This is a more aggressive entry model than LC-2A so you need experience + discipline to 
know when to execute it. 
The following are some potential ideas you may want to test to help make your rules more 
restrictive to limit the number of potentially valid trades:
•  Must be MTF LID 

•  LID must be within the MTF POI (or MTF range POI)
The easiest way to test these is to add them as variables in your backtesting collection.
Then you can filter the results to see how each rule affects the overall statistics.