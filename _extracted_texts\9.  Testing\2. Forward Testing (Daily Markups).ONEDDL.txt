Extracted from: 2. Forward Testing (Daily Markups).ONEDDL.docx
Original file: 9.  Testing/2. Forward Testing (Daily Markups).ONEDDL.docx
Extraction date: 2025-07-06T09:52:18.186Z
File type: DOCX
================================================================================

Forward Testing (Daily Markups)

8. 🧪 TESTING

Forward Testing allows you to collect data/examples as a part of your daily trading process. 

I'd highly recommend you perform this process daily (or weekly if you are a swing trader) for the rest of your trading career. 

Save a new TV chart layout or use your live trading charts

Mark up the relevant analysis for that day (week) on all of the timeframes you use

If you see a reaction happen, place a horizontal line at the apex of it and study what caused the reaction

Mark up your execution LTF with any trades applicable to your plan for the session(s) you trade

Annotate everything and take your time

Keep a record of the analysis & available R on any valid trades 

 

At first, you should have tonnes of annotations on your charts with the goal to really understand the why behind price moves. 

As you progress, you will eventually only mark up new things that you are noticing and stand out to you. 

 

Build rock-solid confidence -> constant repetition, see the same things every single day and notice areas you need to improve on. 

Develop an abundance mindset -> see the insane amount of opportunity there is every single month, building patience & eradicating FOMO.

Acts as a forecast for the next trading day -> constantly building the HTF/MTF story and shows unmitigated POIs. 

 

👉  CLICK HERE to download the notion template IF you don't already have it 👈