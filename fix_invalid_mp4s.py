import os
import subprocess
import tempfile
import shutil

def is_fake_mp4(file_path):
    try:
        with open(file_path, 'rb') as f:
            header = f.read(16)
            return header.startswith(b'G@\x00\x10\x00\x00\xb0\r')
    except Exception as e:
        print(f"⚠ Error reading file: {file_path}\n  ➤ {e}")
        return False

def convert_and_replace(input_path):
    temp_output = os.path.join(tempfile.gettempdir(), os.path.basename(input_path))
    try:
        subprocess.run([
            "ffmpeg", "-y",
            "-i", input_path,
            "-c", "copy",
            temp_output
        ], check=True)

        shutil.move(temp_output, input_path)
        print(f"✅ Replaced: {input_path}")

    except subprocess.CalledProcessError:
        print(f"❌ FFmpeg failed: {input_path}")
    except Exception as e:
        print(f"⚠ Unexpected error: {e}")

def scan_and_fix_all_mp4(base_folder):
    print(f"🔍 Scanning all .mp4 files inside: {base_folder}")
    for root, _, files in os.walk(base_folder):
        for file in files:
            if file.lower().endswith(".mp4"):
                full_path = os.path.join(root, file)
                if is_fake_mp4(full_path):
                    print(f"⚠ Corrupted (MPEG-TS inside .mp4): {full_path}")
                    convert_and_replace(full_path)

if __name__ == "__main__":
    base_folder = os.getcwd()
    scan_and_fix_all_mp4(base_folder)
    print("\n🎉 All done! All nested folders were checked.")
