import os
import subprocess
import tempfile
import shutil

def is_mpeg_ts(file_path):
    try:
        with open(file_path, 'rb') as f:
            header = f.read(4)
            return header.startswith(b'\x47')  # MPEG-TS starts with 0x47
    except:
        return False

def convert_ts_to_mp4(ts_path):
    mp4_path = ts_path.rsplit(".", 1)[0] + ".mp4"
    try:
        subprocess.run([
            "ffmpeg", "-y",
            "-i", ts_path,
            "-c", "copy",
            mp4_path
        ], check=True)
        print(f"✅ Converted: {ts_path} → {mp4_path}")

        os.remove(ts_path)
        print(f"🗑️ Deleted original: {ts_path}")
    except subprocess.CalledProcessError:
        print(f"❌ FFmpeg failed on: {ts_path}")
    except Exception as e:
        print(f"⚠ Error processing {ts_path}: {e}")

def scan_and_convert_ts(base_folder):
    print(f"🔍 Scanning for real .ts video files in: {base_folder}")
    for root, _, files in os.walk(base_folder):
        for file in files:
            if file.lower().endswith(".ts"):
                full_path = os.path.join(root, file)
                if is_mpeg_ts(full_path):
                    convert_ts_to_mp4(full_path)
                else:
                    print(f"🚫 Skipped (not a video): {full_path}")

if __name__ == "__main__":
    base_folder = os.getcwd()
    scan_and_convert_ts(base_folder)
    print("\n🎉 Done! All valid .ts video files were processed.")
