Extracted from: 1. Basic Orderflow.ONEDDL.docx
Original file: 6. Technical Analysis/3. Orderflow 3.0 [2022]/1. Basic Orderflow.ONEDDL.docx
Extraction date: 2025-07-06T09:52:17.821Z
File type: DOCX
================================================================================

Basic Orderflow

ORDERFLOW

 

Orderflow is essentially Supply & Demand ranges.

https://s3.tradingview.com/snapshots/z/zKD8koFg.png



 

Orderflow and Market Structure are basically the exact same concept. 

Orderflow visualises the supply and demand ranges in the market. 

Market structure does the exact same thing (without drawing a coloured box)! 

https://s3.tradingview.com/snapshots/a/a161g8TF.png 



 

Orderflow is simply a concept where we draw a box on a structural range (Swing / Internal / Fractal) to represent the supply or demand of that range.  

https://s3.tradingview.com/snapshots/i/iybR5U9c.png 



We use orderflow to signal the start and end of pullbacks (just like we do with structure breaks). 

Shift in Swing OF -> signals Swing trend change (pullback starting/ending on a higher timeframe)

Shift in Internal OF -> signals Swing pullback starting/ending

Shift in Fractal OF -> signals Internal pullback starting/ending. 

 

Once the Internal & Swing structure aligns and there is a shift in Orderflow, we expect the ranges to be respected until the weak Swing structure is broken on that timeframe. 

i.e we expect strong highs/lows to hold and weak highs/lows to break= we expect the current orderflow ranges to hold 

 

https://s3.tradingview.com/snapshots/y/YYe8SHMG.png 



 

We want to use shifts in Orderflow as useful information in SIGNIFICANT AREAS OF VALUE where we expect price to shift. 

(The same way a CHoCH in the middle of nowhere is not as strong as a signal as a CHoCH that occurs in an extreme pro-trend pivot S&D zone).