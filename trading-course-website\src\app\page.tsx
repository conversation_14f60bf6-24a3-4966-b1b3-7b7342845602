'use client';

import { useState, useEffect } from 'react';
import AuthFlow from '@/components/auth/AuthFlow';
import CourseDashboard from '@/components/course/CourseDashboard';
import { CourseStructure, UserProgress } from '@/types/course';

export default function Home() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [username, setUsername] = useState('');
  const [courseStructure, setCourseStructure] = useState<CourseStructure | null>(null);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadCourseStructure = async () => {
      try {
        const response = await fetch('/api/course-structure');
        const data = await response.json();
        setCourseStructure(data);
      } catch (error) {
        console.error('Failed to load course structure:', error);
        // Fallback structure
        setCourseStructure({
          title: "Photon Trading Zero to Funded 4.0 (2025)",
          modules: [],
          totalModules: 0,
          totalFiles: 0,
          totalVideos: 0,
          totalDocuments: 0,
          estimatedDuration: 0
        });
      }
    };

    loadCourseStructure();
  }, []);

  useEffect(() => {
    if (username && courseStructure) {
      loadUserProgress();
    }
  }, [username, courseStructure]);

  const loadUserProgress = () => {
    const savedProgress = localStorage.getItem(`progress_${username}`);
    if (savedProgress) {
      try {
        setUserProgress(JSON.parse(savedProgress));
      } catch (error) {
        console.error('Failed to parse user progress:', error);
        createNewUserProgress();
      }
    } else {
      createNewUserProgress();
    }
    setIsLoading(false);
  };

  const createNewUserProgress = () => {
    const newProgress: UserProgress = {
      username,
      lastAccessed: new Date().toISOString(),
      watchedVideos: [],
      completedModules: [],
      notes: [],
      totalWatchTime: 0,
      analytics: {
        sessionsCount: 0,
        averageSessionTime: 0,
        focusAreas: [],
        learningStreak: 0
      }
    };
    setUserProgress(newProgress);
    saveUserProgress(newProgress);
  };

  const saveUserProgress = (progress: UserProgress) => {
    localStorage.setItem(`progress_${username}`, JSON.stringify(progress));
    
    // Also save to shared progress for community features
    const sharedProgress = localStorage.getItem('sharedProgress');
    const shared = sharedProgress ? JSON.parse(sharedProgress) : { users: {}, lastUpdated: new Date().toISOString() };
    shared.users[username] = progress;
    shared.lastUpdated = new Date().toISOString();
    localStorage.setItem('sharedProgress', JSON.stringify(shared));
  };

  const handleAuthenticated = (user: string) => {
    setUsername(user);
    setIsAuthenticated(true);
    updateUserAnalytics();
  };

  const updateUserAnalytics = () => {
    if (userProgress) {
      const updatedProgress = {
        ...userProgress,
        lastAccessed: new Date().toISOString(),
        analytics: {
          ...userProgress.analytics,
          sessionsCount: userProgress.analytics.sessionsCount + 1
        }
      };
      setUserProgress(updatedProgress);
      saveUserProgress(updatedProgress);
    }
  };

  const handleModuleClick = (modulePath: string) => {
    if (userProgress) {
      const updatedProgress = {
        ...userProgress,
        currentModule: modulePath,
        lastAccessed: new Date().toISOString()
      };
      setUserProgress(updatedProgress);
      saveUserProgress(updatedProgress);
    }
    
    // Navigate to module view (will implement this next)
    window.location.href = `/course/${encodeURIComponent(modulePath)}`;
  };

  const handleLogout = () => {
    localStorage.removeItem('courseAuth');
    setIsAuthenticated(false);
    setUsername('');
    setUserProgress(null);
  };

  if (!isAuthenticated) {
    return <AuthFlow onAuthenticated={handleAuthenticated} />;
  }

  if (isLoading || !courseStructure || !userProgress) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass-morphism p-8 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
          <p className="text-white">Loading course content...</p>
        </div>
      </div>
    );
  }

  return (
    <CourseDashboard
      username={username}
      courseStructure={courseStructure}
      userProgress={userProgress}
      onModuleClick={handleModuleClick}
      onLogout={handleLogout}
    />
  );
}
