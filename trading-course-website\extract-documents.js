#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const mammoth = require('mammoth');
const pdf = require('pdf-parse');

/**
 * Extract text from different document types
 */
class DocumentExtractor {
  constructor(courseDir) {
    this.courseDir = courseDir;
    this.outputDir = path.join(courseDir, '_extracted_texts');
    this.processedCount = 0;
    this.successCount = 0;
  }

  async extractPdfText(filePath) {
    try {
      const dataBuffer = await fs.readFile(filePath);
      const data = await pdf(dataBuffer);
      
      if (data.text && data.text.trim()) {
        return data.text.trim();
      }
      return null;
    } catch (error) {
      console.error(`PDF extraction failed for ${filePath}:`, error.message);
      return null;
    }
  }

  async extractDocxText(filePath) {
    try {
      const result = await mammoth.extractRawText({ path: filePath });
      
      if (result.value && result.value.trim()) {
        return result.value.trim();
      }
      return null;
    } catch (error) {
      console.error(`DOCX extraction failed for ${filePath}:`, error.message);
      return null;
    }
  }

  async extractTxtText(filePath) {
    try {
      const encodings = ['utf8', 'latin1'];
      
      for (const encoding of encodings) {
        try {
          const text = await fs.readFile(filePath, encoding);
          return text;
        } catch (error) {
          continue;
        }
      }
      return null;
    } catch (error) {
      console.error(`TXT extraction failed for ${filePath}:`, error.message);
      return null;
    }
  }

  async processFile(filePath) {
    const relativePath = path.relative(this.courseDir, filePath);
    const extension = path.extname(filePath).toLowerCase();
    const baseName = path.basename(filePath, extension);
    
    console.log(`Processing: ${relativePath}`);
    this.processedCount++;

    let extractedText = null;

    try {
      switch (extension) {
        case '.pdf':
          extractedText = await this.extractPdfText(filePath);
          break;
        case '.docx':
          extractedText = await this.extractDocxText(filePath);
          break;
        case '.txt':
          extractedText = await this.extractTxtText(filePath);
          break;
        default:
          console.log(`  ⚠️  Unsupported file type: ${extension}`);
          return false;
      }

      if (extractedText && extractedText.trim()) {
        // Create output file path maintaining directory structure
        const outputFilePath = path.join(
          this.outputDir,
          path.dirname(relativePath),
          `${baseName}.txt`
        );

        // Ensure output directory exists
        await fs.ensureDir(path.dirname(outputFilePath));

        // Prepare content with metadata
        const content = [
          `Extracted from: ${path.basename(filePath)}`,
          `Original file: ${relativePath}`,
          `Extraction date: ${new Date().toISOString()}`,
          `File type: ${extension.substring(1).toUpperCase()}`,
          '='.repeat(80),
          '',
          extractedText
        ].join('\n');

        // Write extracted text
        await fs.writeFile(outputFilePath, content, 'utf8');
        
        console.log(`  ✓ Extracted to: ${path.relative(this.courseDir, outputFilePath)}`);
        this.successCount++;
        return true;
      } else {
        console.log(`  ⚠️  No text extracted or file is empty`);
        return false;
      }
    } catch (error) {
      console.error(`  ✗ Error processing file:`, error.message);
      return false;
    }
  }

  async scanAndExtract() {
    console.log('Trading Course Document Text Extractor');
    console.log('='.repeat(50));
    console.log(`Course directory: ${this.courseDir}`);
    console.log(`Output directory: ${this.outputDir}`);

    // Ensure output directory exists
    await fs.ensureDir(this.outputDir);

    // Supported extensions
    const supportedExtensions = ['.pdf', '.docx', '.txt'];
    
    // Find all supported files
    const filesToProcess = [];
    
    const scanDir = async (dir) => {
      const items = await fs.readdir(dir, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(dir, item.name);
        
        if (item.isDirectory()) {
          // Skip node_modules, .git, and extracted texts directories
          if (!['node_modules', '.git', '.next', '_extracted_texts'].includes(item.name)) {
            await scanDir(fullPath);
          }
        } else if (item.isFile()) {
          const ext = path.extname(item.name).toLowerCase();
          if (supportedExtensions.includes(ext) && !item.name.startsWith('_extracted_')) {
            filesToProcess.push(fullPath);
          }
        }
      }
    };

    await scanDir(this.courseDir);

    console.log(`\nFound ${filesToProcess.length} files to process:`);
    filesToProcess.forEach(file => {
      console.log(`  - ${path.relative(this.courseDir, file)}`);
    });

    if (filesToProcess.length === 0) {
      console.log('No files found to process!');
      return;
    }

    console.log(`\nStarting extraction...`);
    console.log('='.repeat(80));

    // Process files
    for (const filePath of filesToProcess) {
      await this.processFile(filePath);
      console.log(''); // Empty line for readability
    }

    console.log('='.repeat(80));
    console.log(`Extraction complete!`);
    console.log(`Successfully processed: ${this.successCount}/${this.processedCount} files`);
    console.log(`Extracted files saved to: ${path.relative(this.courseDir, this.outputDir)}`);
  }
}

// Main execution
async function main() {
  try {
    const courseDir = path.resolve(__dirname, '..');
    const extractor = new DocumentExtractor(courseDir);
    await extractor.scanAndExtract();
  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}