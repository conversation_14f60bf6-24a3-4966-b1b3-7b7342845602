Extracted from: 8. Mechanical Swing Structure.ONEDDL.pdf
Original file: 6. Technical Analysis/1. Market Structure 3.0 [2023]/8. Mechanical Swing Structure.ONEDDL.pdf
Extraction date: 2025-07-06T09:52:17.779Z
File type: PDF
================================================================================

Mechanical Swing Structure
Market Structure 3.0
The only element of discretion we use in mapping Swing structure is defining what is 
'significant' enough of a pullback to define it as a Swing pullback. 
When you have a solid understanding of the three types of structure as well as multi-timeframe 
analysis (which you will learn in the following lessons) you will see that it doesn't make 
a massive difference on exactly how you have mapped your Swing structure (whether you look 
at small or larger swings - I'll prove this to you in a lesson later in this module).
However, when you are starting out, it may help you to be more mechanical with defining what 
is and isn't a Swing pullback. We recommend you do this by having minimum pip size rules to 
measure Swing pullbacks for each timeframe and instrument you use. 
 
HOW TO MAKE PIP RULES:
Test this yourself.
1. Draw on all structure that looks like a 'significant' enough pullback to be considered a Swing 
for you to your 'naked' eye. Do this for about 15-20 examples from current price action.
2. Then measure them and see what the smallest pip size is of those pullbacks. ￿
3. Now you have your pip rule for that timeframe on that instrument/pair.
It's a very quick process and you can easily adjust the rule if you find it's either too aggressive or 
too conservative for that pair and/or your style.
Pip rules are not some ‘magical’ number. It’s simply a tool to help you be consistent and 
mechanical with structure mapping. As you gain more experience, you may feel comfortable 
using an element of discretion. 
 
DRAWBACKS
The major drawbacks to using fixed pip rules:
•  volatility changes over time

•  every instrument has different volatility 
If price becomes more or less volatile (how many pips price typically moves in a day) then you 
can adjust your pip rules to be relevant to the most current levels of volatility. 
You will need to have set rules for every timeframe and instrument you trade. 
 
You may want to explore and test using volatility-based measurements such as ATR (average 
true range) or Fibonacci retracements instead of using fixed pip size rules. This is not something 
I've tested personally to date.