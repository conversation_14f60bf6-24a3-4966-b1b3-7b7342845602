export const PREDEFINED_MODULES = [
  {
    id: 1,
    name: "Start Here",
    description: "Welcome to Photon Trading - Your journey begins",
    folderPattern: "1. Start Here",
    color: "#667eea"
  },
  {
    id: 2,
    name: "Intro To FX",
    description: "Fundamentals of Foreign Exchange Trading",
    folderPattern: "2.  Intro To FX",
    color: "#764ba2"
  },
  {
    id: 3,
    name: "Risk Management",
    description: "Essential risk management strategies",
    folderPattern: "3. Risk Management",
    color: "#f093fb"
  },
  {
    id: 4,
    name: "Brokers & Charting",
    description: "Choosing brokers and setting up charts",
    folderPattern: "4.  Brokers & Charting",
    color: "#f5576c"
  },
  {
    id: 5,
    name: "Trading An Edge",
    description: "Developing your trading edge",
    folderPattern: "5.  Trading An Edge",
    color: "#4facfe"
  },
  {
    id: 6,
    name: "Technical Analysis",
    description: "Advanced technical analysis techniques",
    folderPattern: "6. Technical Analysis",
    color: "#43e97b"
  },
  {
    id: 7,
    name: "PHOTON STRATEGY",
    description: "The complete Photon trading strategy",
    folderPattern: "7.  PHOTON STRATEGY (2024)",
    color: "#38f9d7"
  },
  {
    id: 8,
    name: "Trade Plan",
    description: "Creating and executing trade plans",
    folderPattern: "8.  TRADE PLAN",
    color: "#5ee7df"
  },
  {
    id: 9,
    name: "Testing",
    description: "Backtesting and forward testing strategies",
    folderPattern: "9.  Testing",
    color: "#66a6ff"
  },
  {
    id: 10,
    name: "Journaling",
    description: "Trading journal and performance tracking",
    folderPattern: "10.   Journaling",
    color: "#89f7fe"
  },
  {
    id: 11,
    name: "Moving Forward",
    description: "Advanced concepts and next steps",
    folderPattern: "11.  Moving Forward",
    color: "#b721ff"
  },
  {
    id: 12,
    name: "Trading Psychology",
    description: "Mastering the mental game of trading",
    folderPattern: "12.  Trading Psychology",
    color: "#21d4fd"
  },
  {
    id: 13,
    name: "Archive",
    description: "Additional resources and advanced content",
    folderPattern: "13. Archive",
    color: "#667eea"
  }
];